.add-product-form {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.card-content {
  padding: 1.5rem;
}

.form-note {
  margin-bottom: 1.5rem;
  padding: 12px 16px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  color: #0c4a6e;
}

.form-note p {
  margin: 0;
  font-size: 14px;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.checkbox-container {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.error-message {
  color: #e53e3e;
  margin-top: 16px;
  padding: 12px;
  background-color: #fff5f5;
  border-radius: 4px;
  border-left: 4px solid #e53e3e;
}

.success-message {
  color: #38a169;
  margin-top: 16px;
  padding: 16px;
  background-color: #f0fff4;
  border-radius: 8px;
  border-left: 4px solid #38a169;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.success-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.success-icon {
  font-size: 20px;
  line-height: 1;
  margin-top: 2px;
}

.success-text {
  flex: 1;
}

.success-text strong {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
}

.success-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Image upload styles */
.image-upload-container {
  margin-bottom: 20px;
  width: 100%;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-inner);
  padding: 1rem;
  background-color: var(--color-background-secondary);
}

.image-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.image-upload-label {
  font-weight: 600;
  color: #374151;
}

.image-upload-buttons {
  display: flex;
  gap: 8px;
}

.hidden-file-input {
  display: none;
}

.upload-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--color-primary);
  color: white;
  font-size: 0.875rem;
  border-radius: var(--border-radius-inner);
  cursor: pointer;
  transition: background-color 0.2s;
}

.upload-button:hover {
  background-color: var(--color-primary-dark);
}

.clear-image-button {
  padding: 0.5rem 1rem;
  background-color: var(--color-border);
  color: var(--color-text-primary);
  font-size: 0.875rem;
  border: none;
  border-radius: var(--border-radius-inner);
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-image-button:hover {
  background-color: var(--color-destructive-light);
  color: var(--color-destructive);
}

.image-preview-container {
  width: 100%;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin-bottom: 12px;
}

.image-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-placeholder {
  color: #9ca3af;
  font-size: 14px;
}

.image-url-input {
  margin-top: 1rem;
}

/* New styles for the updated layout */
.form-section {
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.form-section h3 {
  margin: 0;
  padding: 12px 16px;
  background-color: #f9fafb;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.form-section-content {
  padding: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row > * {
  flex: 1;
}

.expandable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.expandable-icon {
  display: flex;
  align-items: center;
  margin-right: 16px;
  color: #6b7280;
}

/* Make form layout responsive */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .error-message {
    background-color: rgba(var(--color-destructive-rgb), 0.2);
  }
  
  .success-message {
    background-color: rgba(var(--color-success-rgb), 0.2);
    color: var(--color-success);
  }
  
  .image-upload-container {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .clear-image-button {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .clear-image-button:hover {
    background-color: rgba(var(--color-destructive-rgb), 0.2);
  }
  
  .image-preview-container {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: var(--color-border);
  }
} 