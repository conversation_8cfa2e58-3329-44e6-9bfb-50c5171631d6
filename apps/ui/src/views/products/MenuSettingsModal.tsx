import React, { useState, useEffect, useRef } from "react";
import Modal from "../../ui/Modal";
import Button from "../../ui/Button";
import TextInput from "../../ui/form/TextInput";
import { SingleSelect } from "../../ui/form/SingleSelect";
import CheckboxInput from "../../ui/form/CheckboxInput";
import { PlusIcon, EditIcon, TrashIcon } from "../../ui/icons";
import { toast } from "react-hot-toast";
import { MenuSetting, menuSettingsApi } from "../../api";

interface MenuSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  locationId: number;
}

const MenuSettingsModal: React.FC<MenuSettingsModalProps> = ({
  isOpen,
  onClose,
  locationId,
}) => {
  const [settings, setSettings] = useState<MenuSetting[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingSetting, setEditingSetting] = useState<MenuSetting | null>(
    null
  );
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<Partial<MenuSetting>>({
    type: "carousel_slide",
    title: "",
    description: "",
    image_url: "",
    link: "",
    order: 0,
    active: true,
  });
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const menuTypes = [
    { value: "carousel_slide", label: "Carousel Slide" },
    { value: "promo_content", label: "Promotional Content" },
    { value: "announcement", label: "Announcement" },
  ];

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const result = await menuSettingsApi.search(locationId, { limit: 100 });
      setSettings(result.results || []);
    } catch (err) {
      console.error(err);
      toast.error("Failed to load menu settings");
      // Ensure settings is still an array even if the API call fails
      setSettings([]);
    } finally {
      setLoading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setImageFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
        // Clear the image_url field when a file is selected
        setFormData({
          ...formData,
          image_url: "",
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const clearImage = () => {
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const clearAllImages = () => {
    setImageFile(null);
    setImagePreview(null);
    setFormData({ ...formData, image_url: "" });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchSettings();
    }
  }, [isOpen, locationId]);

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Create clean data object excluding server-managed fields
      const menuSettingData = {
        type: formData.type || "carousel_slide",
        title: formData.title || "",
        description: formData.description,
        image_url: formData.image_url,
        link: formData.link,
        order: formData.order || 0,
        active: formData.active ?? true,
        start_date: formData.start_date,
        end_date: formData.end_date,
        metadata: formData.metadata,
      };

      if (imageFile) {
        // Use multipart form data with file upload
        const formDataToSend = new FormData();

        // Add all menu setting data fields
        Object.entries(menuSettingData).forEach(([key, value]) => {
          if (value !== null && value !== undefined && key !== "location_id") {
            if (Array.isArray(value) || typeof value === "object") {
              formDataToSend.append(key, JSON.stringify(value));
            } else {
              formDataToSend.append(key, String(value));
            }
          }
        });

        // Add the image file
        formDataToSend.append("menuImage", imageFile);

        // Create or update menu setting with image in one request
        const url = editingSetting
          ? `/api/admin/locations/${locationId}/menu-settings/${editingSetting.id}`
          : `/api/admin/locations/${locationId}/menu-settings`;

        const method = editingSetting ? "PUT" : "POST";

        const response = await fetch(url, {
          method,
          body: formDataToSend,
          headers: {
            Accept: "application/json",
          },
          credentials: "include",
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `Request failed: ${response.statusText}`
          );
        }

        const result = await response.json();
        console.log("Menu setting saved successfully:", result);
      } else if (formData.image_url) {
        // Use download_image_url for URL-based images
        const menuSettingDataWithDownload = {
          ...menuSettingData,
          download_image_url: formData.image_url,
        };
        delete menuSettingDataWithDownload.image_url; // Remove image_url since we're using download_image_url

        if (editingSetting) {
          await menuSettingsApi.update(
            locationId,
            editingSetting.id,
            menuSettingDataWithDownload as any
          );
        } else {
          await menuSettingsApi.create(
            locationId,
            menuSettingDataWithDownload as any
          );
        }
      } else {
        // Regular request without image
        if (editingSetting) {
          await menuSettingsApi.update(
            locationId,
            editingSetting.id,
            menuSettingData as any
          );
        } else {
          await menuSettingsApi.create(locationId, menuSettingData as any);
        }
      }

      toast.success(
        editingSetting
          ? "Menu setting updated successfully"
          : "Menu setting created successfully"
      );

      await fetchSettings();
      handleCloseForm();
    } catch (err) {
      console.error(err);
      toast.error("Failed to save menu setting");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm("Are you sure you want to delete this menu setting?")) {
      return;
    }

    try {
      setLoading(true);
      await menuSettingsApi.delete(locationId, id);
      toast.success("Menu setting deleted successfully");
      await fetchSettings();
    } catch (err) {
      console.error(err);
      toast.error("Failed to delete menu setting");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (setting: MenuSetting) => {
    setEditingSetting(setting);
    setFormData({
      type: setting.type,
      title: setting.title || "",
      description: setting.description,
      image_url: setting.image_url,
      link: setting.link,
      order: setting.order,
      active: setting.active,
      start_date: setting.start_date,
      end_date: setting.end_date,
      metadata: setting.metadata,
    });

    // Set image preview if there's an image URL
    if (setting.image_url) {
      setImagePreview(setting.image_url);
    } else {
      setImagePreview(null);
    }
    setImageFile(null);

    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingSetting(null);
    setFormData({
      type: "carousel_slide",
      title: "",
      description: "",
      image_url: "",
      link: "",
      order: 0,
      active: true,
    });
    clearAllImages();
  };

  const handleAddNew = () => {
    setEditingSetting(null);

    // Safely calculate the next order value
    const maxOrder =
      settings && settings.length > 0
        ? Math.max(...settings.map((s) => s.order || 0))
        : 0;

    setFormData({
      type: "carousel_slide",
      title: "",
      description: "",
      image_url: "",
      link: "",
      order: maxOrder + 1,
      active: true,
    });
    setShowForm(true);
  };

  if (!isOpen) return null;

  return (
    <Modal title="Menu Settings" open={isOpen} onClose={onClose} size="large">
      <div className="space-y-6">
        {/* Header with Add Button */}
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Manage Menu Content</h3>
          <Button icon={<PlusIcon />} onClick={handleAddNew}>
            Add Setting
          </Button>
        </div>

        {/* Settings List */}
        {loading ? (
          <div className="text-center py-8">Loading...</div>
        ) : (
          <div className="space-y-4">
            {settings.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No menu settings found. Click "Add Setting" to create one.
              </div>
            ) : (
              settings.map((setting) => (
                <div
                  key={setting.id}
                  className="border rounded-lg p-4 bg-white shadow-sm"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {setting.type.replace("_", " ")}
                        </span>
                        {!setting.active && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Inactive
                          </span>
                        )}
                        <span className="text-sm text-gray-500">
                          Order: {setting.order}
                        </span>
                      </div>
                      <h4 className="font-medium">{setting.title}</h4>
                      {setting.description && (
                        <p className="text-sm text-gray-600 mt-1">
                          {setting.description}
                        </p>
                      )}
                      {setting.image_url && (
                        <div className="mt-2">
                          <img
                            src={setting.image_url}
                            alt={setting.title}
                            className="h-16 w-auto rounded object-cover"
                          />
                        </div>
                      )}
                      {(setting.start_date || setting.end_date) && (
                        <div className="text-xs text-gray-500 mt-2">
                          {setting.start_date && (
                            <span>
                              From:{" "}
                              {new Date(
                                setting.start_date
                              ).toLocaleDateString()}
                            </span>
                          )}
                          {setting.start_date && setting.end_date && " | "}
                          {setting.end_date && (
                            <span>
                              To:{" "}
                              {new Date(setting.end_date).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="secondary"
                        size="small"
                        icon={<EditIcon />}
                        onClick={() => handleEdit(setting)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="small"
                        icon={<TrashIcon />}
                        onClick={() => handleDelete(setting.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Add/Edit Form Modal */}
        {showForm && (
          <Modal
            title={editingSetting ? "Edit Menu Setting" : "Add Menu Setting"}
            open={showForm}
            onClose={handleCloseForm}
          >
            <div className="space-y-4">
              <SingleSelect
                label="Type"
                value={formData.type || "carousel_slide"}
                onChange={(value: string) =>
                  setFormData({ ...formData, type: value })
                }
                required
                options={menuTypes}
                getOptionDisplay={(option) => option.label}
                toValue={(option) => option.value}
              />

              <TextInput<string>
                name="title"
                label="Title"
                value={formData.title || ""}
                onChange={(value) => setFormData({ ...formData, title: value })}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  className="w-full min-h-[80px] p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={formData.description}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Optional description..."
                />
              </div>

              {/* Image Upload Section */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700">
                  Image
                </label>

                <div className="border border-gray-300 rounded-md p-4 space-y-3">
                  <div className="flex gap-2">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleImageChange}
                      id="menu-image"
                    />
                    <Button
                      type="button"
                      variant="secondary"
                      size="small"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      Select Image
                    </Button>
                    {imagePreview && (
                      <Button
                        type="button"
                        variant="secondary"
                        size="small"
                        onClick={clearAllImages}
                      >
                        Clear
                      </Button>
                    )}
                  </div>

                  {imagePreview && (
                    <div className="border border-gray-200 rounded p-2">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="max-w-full max-h-32 object-contain"
                      />
                    </div>
                  )}

                  <div className="text-sm text-gray-500">
                    Or enter image URL below
                  </div>

                  <TextInput
                    name="image_url"
                    label="Image URL"
                    value={formData.image_url}
                    onChange={(value: string) => {
                      setFormData({ ...formData, image_url: value });
                      if (value && value.trim() !== "") {
                        setImagePreview(value);
                        clearImage(); // Clear file when URL is provided
                      }
                    }}
                    placeholder="https://example.com/image.jpg"
                    disabled={!!imageFile}
                  />

                  {imageFile && (
                    <div className="text-sm text-gray-500">
                      File selected: {imageFile.name}
                    </div>
                  )}
                </div>
              </div>

              <TextInput
                name="link"
                label="Link URL"
                value={formData.link}
                onChange={(value: string) =>
                  setFormData({ ...formData, link: value })
                }
                placeholder="https://example.com"
              />

              <TextInput<number>
                name="order"
                label="Order"
                type="number"
                min={0}
                value={formData.order ?? 0}
                onChange={(value) => setFormData({ ...formData, order: value })}
              />

              <div className="flex items-center pt-6">
                <CheckboxInput
                  label="Active"
                  checked={formData.active}
                  onChange={(value) =>
                    setFormData({ ...formData, active: value })
                  }
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <TextInput
                  name="start_date"
                  label="Start Date"
                  type="date"
                  value={formData.start_date}
                  onChange={(value) =>
                    setFormData({ ...formData, start_date: value })
                  }
                />

                <TextInput
                  name="end_date"
                  label="End Date"
                  type="date"
                  value={formData.end_date}
                  onChange={(value) =>
                    setFormData({ ...formData, end_date: value })
                  }
                />
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button
                  variant="secondary"
                  onClick={handleCloseForm}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button onClick={handleSubmit} disabled={loading}>
                  {loading ? "Saving..." : editingSetting ? "Update" : "Create"}
                </Button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </Modal>
  );
};

export default MenuSettingsModal;
