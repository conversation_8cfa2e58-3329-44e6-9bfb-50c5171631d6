import { useCallback, useEffect, useMemo, useRef, useState } from "react";

export function useResolver<T>(resolver: () => Promise<T>) {
  const [value, setValue] = useState<null | T>(null);
  const resolverRef = useRef(resolver);
  const isMountedRef = useRef(true);

  // Update the resolver ref when it changes
  resolverRef.current = resolver;

  const reload = useCallback(async () => {
    try {
      const result = await resolverRef.current();
      if (isMountedRef.current) {
        setValue(result);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  // Only run on mount and when resolver reference changes
  useEffect(() => {
    reload();
  }, [reload, resolver]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return useMemo(() => [value, setValue, reload] as const, [value, reload]);
}

export function useDebounceControl<T>(
  value: T,
  onChange: (value: T) => void,
  ms = 400
) {
  const changeRef = useRef(onChange);
  changeRef.current = onChange;
  const valueRef = useRef(value);
  valueRef.current = value;
  const timeoutId = useRef<ReturnType<typeof setTimeout>>();
  const synced = useRef(true);
  const [temp, setTemp] = useState<T>(value);
  useEffect(() => {
    clearTimeout(timeoutId.current);
    if (valueRef.current !== temp) {
      timeoutId.current = setTimeout(() => {
        changeRef.current(temp);
        synced.current = false;
      }, ms);
    }
  }, [temp, ms]);
  useEffect(() => {
    if (!synced.current) {
      setTemp(value);
      synced.current = true;
    }
  }, [value]);
  return [temp, setTemp] as const;
}
