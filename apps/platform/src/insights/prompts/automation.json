{"base_prompt": "You are a marketing automation expert creating step-by-step automation plans. \nYour task is to create practical, efficient automation plans that follow our system's constraints.\n\nIMPORTANT - How the system works:\n1. The frontend receives your JSON plan and processes each item in sequential order\n2. Each item depends on previous items being completed first\n3. Items are stored in a dictionary with unique keys, not an array\n4. Lists can be referenced in two ways (never both):\n   - For new lists created in your plan: Use list_refs: [\"listKey\"] where listKey is the key of the list in the items dictionary\n   - For existing lists: Use list_ids: [number]\n5. Templates must reference campaigns using ONE of these approaches (never both):\n   - For new campaigns: Use campaign_ref: \"campaignKey\" where campaignKey is the key of the campaign in the items dictionary\n   - For existing campaigns: Use campaign_id: number\n\nItems must be created in this order:\n1. Lists (if needed)\n2. Campaigns\n3. Templates (referencing campaigns from above)\n\nReturn a valid JSON object containing:\n- name: string\n- description: string\n- items: dictionary of named items", "constraints": ["Each item must follow these rules:\n1. List items:\n   - Must have config.list object\n   - Must include name, type, and description\n   - Dynamic lists must have a valid rule\n\n2. Campaign items:\n   - Must have config.campaign object\n   - Must specify type, name, channel, subscription_id, provider_id\n   - Must include either list_refs (array of keys) or list_ids (array of numbers), never both\n   - Can optionally include exclusion_list_refs (array of keys) or exclusion_list_ids (array of numbers), never both\n\n3. Template items:\n   - Must have config.template object\n   - Must match campaign channel type (email/text)\n   - Must reference campaign using ONE of:\n     a) campaign_ref: \"campaignKey\" for new campaigns\n     b) campaign_id: number for existing campaigns\n   - Never use both campaign_ref and campaign_id\n   - Email templates require from.name, from.address, reply_to\n   - Text templates require text content", "Validation requirements:\n1. No undefined or null values (use empty string \"\" instead)\n2. Use ISO date strings (e.g. \"2025-01-02T00:00:00Z\")\n3. All required fields must be present\n4. Campaign references must be correct:\n   - New campaigns: Use campaign_ref: \"campaignKey\"\n   - Existing campaigns: Use numeric ID only\n5. Template items must reference campaign items that come BEFORE them in the dictionary"], "training_examples": [{"input": {"insight": {"id": 33, "location_id": 1, "title": "Cross-Selling Opportunities Based on Purchase History", "description": "Analyze purchase history to identify cross-selling opportunities. Customers who purchased high-value items may be interested in complementary products. For instance, <PERSON> could be targeted with related high-end products to enhance their shopping experience.", "impact": "high", "type": "automation", "actions": ["Review purchase histories for high-value customers", "Create cross-sell product recommendations", "Automate email campaigns with these recommendations"], "plan": {"name": "Cross-Selling Automation Plan", "items": {"journeyCrossSelling": {"type": "journey", "config": {"journey": {"name": "Cross-Selling Customer Journey", "tags": ["cross-sell", "customer-engagement", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Journey Start", "type": "entrance", "children": []}}, "published": false, "description": "Journey to engage high-value customers with targeted cross-selling emails to complementary products while respecting privacy and professionalism."}}}, "listHighAOVCustomers": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "purchase_history.high_value_purchases", "type": "string", "uuid": "auto-generated-uuid-001", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["cross-sell", "customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value for targeted cross-selling"}}}, "campaignCrossSellEmail": {"type": "campaign", "config": {"campaign": {"name": "Cross-Sell Complementary Products Campaign", "tags": ["cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVCustomers"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateCrossSellEmail": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear Valued Customer,</p><p>Thank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.</p><p>Visit our store to discover more.</p><p>Best regards,<br/>The Premium Products Team</p></body></html>", "name": "Cross-Sell Complementary Products Email Template", "text": "Dear Valued Customer,\n\nThank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.\n\nVisit our store to discover more.\n\nBest regards,\nThe Premium Products Team", "editor": "code", "subject": "Enhance Your Experience with Exclusive Complementary Products", "reply_to": "<EMAIL>"}, "name": "Cross-Sell Complementary Products Email Template", "type": "email", "locale": "en", "campaign_ref": "campaignCrossSellEmail"}}}}, "description": "Automation plan to identify and engage customers with high-value purchases for cross-selling complementary high-end products via email while ensuring privacy and professionalism."}, "status": "new", "created_at": "2025-06-14T15:31:54.000Z", "updated_at": "2025-06-14T15:47:44.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Cross-Selling Automation Plan", "items": {"journeyCrossSelling": {"type": "journey", "config": {"journey": {"name": "Cross-Selling Customer Journey", "tags": ["cross-sell", "customer-engagement", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Journey Start", "type": "entrance", "children": []}}, "published": false, "description": "Journey to engage high-value customers with targeted cross-selling emails to complementary products while respecting privacy and professionalism."}}}, "listHighAOVCustomers": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "purchase_history.high_value_purchases", "type": "string", "uuid": "auto-generated-uuid-001", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["cross-sell", "customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value for targeted cross-selling"}}}, "campaignCrossSellEmail": {"type": "campaign", "config": {"campaign": {"name": "Cross-Sell Complementary Products Campaign", "tags": ["cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVCustomers"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateCrossSellEmail": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear Valued Customer,</p><p>Thank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.</p><p>Visit our store to discover more.</p><p>Best regards,<br/>The Premium Products Team</p></body></html>", "name": "Cross-Sell Complementary Products Email Template", "text": "Dear Valued Customer,\n\nThank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.\n\nVisit our store to discover more.\n\nBest regards,\nThe Premium Products Team", "editor": "code", "subject": "Enhance Your Experience with Exclusive Complementary Products", "reply_to": "<EMAIL>"}, "name": "Cross-Sell Complementary Products Email Template", "type": "email", "locale": "en", "campaign_ref": "campaignCrossSellEmail"}}}}, "description": "Automation plan to identify and engage customers with high-value purchases for cross-selling complementary high-end products via email while ensuring privacy and professionalism."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Cross-Selling Automation Plan", "items": {"journeyCrossSelling": {"type": "journey", "config": {"journey": {"name": "Cross-Selling Customer Journey", "tags": ["cross-sell", "customer-engagement", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Journey Start", "type": "entrance", "children": []}}, "published": false, "description": "Journey to engage high-value customers with targeted cross-selling emails to complementary products while respecting privacy and professionalism."}}}, "listHighAOVCustomers": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "purchase_history.high_value_purchases", "type": "string", "uuid": "auto-generated-uuid-001", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["cross-sell", "customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value for targeted cross-selling"}}}, "campaignCrossSellEmail": {"type": "campaign", "config": {"campaign": {"name": "Cross-Sell Complementary Products Campaign", "tags": ["cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVCustomers"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateCrossSellEmail": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear Valued Customer,</p><p>Thank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.</p><p>Visit our store to discover more.</p><p>Best regards,<br/>The Premium Products Team</p></body></html>", "name": "Cross-Sell Complementary Products Email Template", "text": "Dear Valued Customer,\n\nThank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.\n\nVisit our store to discover more.\n\nBest regards,\nThe Premium Products Team", "editor": "code", "subject": "Enhance Your Experience with Exclusive Complementary Products", "reply_to": "<EMAIL>"}, "name": "Cross-Sell Complementary Products Email Template", "type": "email", "locale": "en", "campaign_ref": "campaignCrossSellEmail"}}}}, "description": "Automation plan to identify and engage customers with high-value purchases for cross-selling complementary high-end products via email while ensuring privacy and professionalism."}, "success": true}, {"input": {"insight": {"id": 33, "location_id": 1, "title": "Cross-Selling Opportunities Based on Purchase History", "description": "Analyze purchase history to identify cross-selling opportunities. Customers who purchased high-value items may be interested in complementary products. For instance, <PERSON> could be targeted with related high-end products to enhance their shopping experience.", "impact": "high", "type": "automation", "actions": ["Review purchase histories for high-value customers", "Create cross-sell product recommendations", "Automate email campaigns with these recommendations"], "plan": {"name": "Cross-Selling Automation Plan", "items": {"journeyCrossSelling": {"type": "journey", "config": {"journey": {"name": "Cross-Selling Customer Journey", "tags": ["cross-sell", "customer-engagement", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Journey Start", "type": "entrance", "children": []}}, "published": false, "description": "Journey to engage high-value customers with targeted cross-selling emails to complementary products while respecting privacy and professionalism."}}}, "listHighAOVCustomers": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "purchase_history.high_value_purchases", "type": "string", "uuid": "auto-generated-uuid-001", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["cross-sell", "customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value for targeted cross-selling"}}}, "campaignCrossSellEmail": {"type": "campaign", "config": {"campaign": {"name": "Cross-Sell Complementary Products Campaign", "tags": ["cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVCustomers"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateCrossSellEmail": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear Valued Customer,</p><p>Thank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.</p><p>Visit our store to discover more.</p><p>Best regards,<br/>The Premium Products Team</p></body></html>", "name": "Cross-Sell Complementary Products Email Template", "text": "Dear Valued Customer,\n\nThank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.\n\nVisit our store to discover more.\n\nBest regards,\nThe Premium Products Team", "editor": "code", "subject": "Enhance Your Experience with Exclusive Complementary Products", "reply_to": "<EMAIL>"}, "name": "Cross-Sell Complementary Products Email Template", "type": "email", "locale": "en", "campaign_ref": "campaignCrossSellEmail"}}}}, "description": "Automation plan to identify and engage customers with high-value purchases for cross-selling complementary high-end products via email while ensuring privacy and professionalism."}, "status": "new", "created_at": "2025-06-14T15:31:54.000Z", "updated_at": "2025-06-14T15:47:44.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Cross-Selling Automation Plan", "items": {"journeyCrossSelling": {"type": "journey", "config": {"journey": {"name": "Cross-Selling Customer Journey", "tags": ["cross-sell", "customer-engagement", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Journey Start", "type": "entrance", "children": []}}, "published": false, "description": "Journey to engage high-value customers with targeted cross-selling emails to complementary products while respecting privacy and professionalism."}}}, "listHighAOVCustomers": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "purchase_history.high_value_purchases", "type": "string", "uuid": "auto-generated-uuid-001", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["cross-sell", "customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value for targeted cross-selling"}}}, "campaignCrossSellEmail": {"type": "campaign", "config": {"campaign": {"name": "Cross-Sell Complementary Products Campaign", "tags": ["cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVCustomers"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateCrossSellEmail": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear Valued Customer,</p><p>Thank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.</p><p>Visit our store to discover more.</p><p>Best regards,<br/>The Premium Products Team</p></body></html>", "name": "Cross-Sell Complementary Products Email Template", "text": "Dear Valued Customer,\n\nThank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.\n\nVisit our store to discover more.\n\nBest regards,\nThe Premium Products Team", "editor": "code", "subject": "Enhance Your Experience with Exclusive Complementary Products", "reply_to": "<EMAIL>"}, "name": "Cross-Sell Complementary Products Email Template", "type": "email", "locale": "en", "campaign_ref": "campaignCrossSellEmail"}}}}, "description": "Automation plan to identify and engage customers with high-value purchases for cross-selling complementary high-end products via email while ensuring privacy and professionalism."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Cross-Selling Automation Plan", "items": {"journeyCrossSelling": {"type": "journey", "config": {"journey": {"name": "Cross-Selling Customer Journey", "tags": ["cross-sell", "customer-engagement", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Journey Start", "type": "entrance", "children": []}}, "published": false, "description": "Journey to engage high-value customers with targeted cross-selling emails to complementary products while respecting privacy and professionalism."}}}, "listHighAOVCustomers": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "purchase_history.high_value_purchases", "type": "string", "uuid": "auto-generated-uuid-001", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["cross-sell", "customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value for targeted cross-selling"}}}, "campaignCrossSellEmail": {"type": "campaign", "config": {"campaign": {"name": "Cross-Sell Complementary Products Campaign", "tags": ["cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVCustomers"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateCrossSellEmail": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear Valued Customer,</p><p>Thank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.</p><p>Visit our store to discover more.</p><p>Best regards,<br/>The Premium Products Team</p></body></html>", "name": "Cross-Sell Complementary Products Email Template", "text": "Dear Valued Customer,\n\nThank you for your recent purchase of our premium products. To enhance your experience, we have curated a selection of complementary high-end items just for you. Explore these exclusive offerings to elevate your lifestyle.\n\nVisit our store to discover more.\n\nBest regards,\nThe Premium Products Team", "editor": "code", "subject": "Enhance Your Experience with Exclusive Complementary Products", "reply_to": "<EMAIL>"}, "name": "Cross-Sell Complementary Products Email Template", "type": "email", "locale": "en", "campaign_ref": "campaignCrossSellEmail"}}}}, "description": "Automation plan to identify and engage customers with high-value purchases for cross-selling complementary high-end products via email while ensuring privacy and professionalism."}, "success": true}, {"input": {"insight": {"id": 39, "location_id": 1, "title": "Re-engagement Strategies for Inactive Customers", "description": "Implement re-engagement strategies for customers who have made purchases but have not returned recently. For instance, customers with one order in the last 30 days should receive targeted offers to entice them back. Define specific offers and track their effectiveness in driving repeat purchases.", "impact": "medium", "type": "campaign", "actions": ["Identify customers with one-time purchases", "Create tailored offers to incentivize return visits", "Track success rates of re-engagement campaigns"], "plan": {"name": "Re-engagement Campaign for Inactive Customers", "items": {"reengagementCampaign": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["inactiveCustomersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "reengagementTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Thank you for your recent purchase. To show our appreciation, here’s an exclusive 15% discount on your next order. Use code WELCOME15 at checkout. Hurry, offer valid for a limited time!</p><p>We look forward to seeing you again soon.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer <PERSON><PERSON>", "text": "We miss you! Thank you for your recent purchase. Enjoy 15% off your next order with code WELCOME15. Limited time offer. We look forward to your next visit. Best regards, Your Company Team", "editor": "code", "subject": "Special Offer Just for You - Come Back and Save!", "reply_to": "<EMAIL>"}, "name": "Re-engagement Offer <PERSON><PERSON>", "type": "email", "locale": "en", "campaign_ref": "reengagementCampaign"}}}, "inactiveCustomersList": {"type": "list", "config": {"list": {"name": "Inactive Customers Last 30 Days", "rule": {"path": "purchase_count_last_30_days", "type": "string", "uuid": "123e4567-e89b-12d3-a456-************", "group": "user", "value": "1", "children": [], "operator": "="}, "tags": ["customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with exactly one order in the last 30 days who have not returned recently."}}}}, "description": "Target customers who made a single purchase in the last 30 days with a special offer email to encourage repeat purchases."}, "status": "new", "created_at": "2025-06-16T11:43:00.000Z", "updated_at": "2025-06-16T12:21:26.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Re-engagement Campaign for Inactive Customers", "items": {"reengagementCampaign": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["inactiveCustomersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "reengagementTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Thank you for your recent purchase. To show our appreciation, here’s an exclusive 15% discount on your next order. Use code WELCOME15 at checkout. Hurry, offer valid for a limited time!</p><p>We look forward to seeing you again soon.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer <PERSON><PERSON>", "text": "We miss you! Thank you for your recent purchase. Enjoy 15% off your next order with code WELCOME15. Limited time offer. We look forward to your next visit. Best regards, Your Company Team", "editor": "code", "subject": "Special Offer Just for You - Come Back and Save!", "reply_to": "<EMAIL>"}, "name": "Re-engagement Offer <PERSON><PERSON>", "type": "email", "locale": "en", "campaign_ref": "reengagementCampaign"}}}, "inactiveCustomersList": {"type": "list", "config": {"list": {"name": "Inactive Customers Last 30 Days", "rule": {"path": "purchase_count_last_30_days", "type": "string", "uuid": "123e4567-e89b-12d3-a456-************", "group": "user", "value": "1", "children": [], "operator": "="}, "tags": ["customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with exactly one order in the last 30 days who have not returned recently."}}}}, "description": "Target customers who made a single purchase in the last 30 days with a special offer email to encourage repeat purchases."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}, {"id": 6, "name": "Inactive Customers Last 30 Days", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}, {"id": 9, "name": "Re-engagement Offer Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}, {"id": 8, "type": "email", "campaign_id": 9}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Re-engagement Campaign for Inactive Customers", "items": {"reengagementCampaign": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["inactiveCustomersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "reengagementTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Thank you for your recent purchase. To show our appreciation, here’s an exclusive 15% discount on your next order. Use code WELCOME15 at checkout. Hurry, offer valid for a limited time!</p><p>We look forward to seeing you again soon.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer <PERSON><PERSON>", "text": "We miss you! Thank you for your recent purchase. Enjoy 15% off your next order with code WELCOME15. Limited time offer. We look forward to your next visit. Best regards, Your Company Team", "editor": "code", "subject": "Special Offer Just for You - Come Back and Save!", "reply_to": "<EMAIL>"}, "name": "Re-engagement Offer <PERSON><PERSON>", "type": "email", "locale": "en", "campaign_ref": "reengagementCampaign"}}}, "inactiveCustomersList": {"type": "list", "config": {"list": {"name": "Inactive Customers Last 30 Days", "rule": {"path": "purchase_count_last_30_days", "type": "string", "uuid": "123e4567-e89b-12d3-a456-************", "group": "user", "value": "1", "children": [], "operator": "="}, "tags": ["customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with exactly one order in the last 30 days who have not returned recently."}}}}, "description": "Target customers who made a single purchase in the last 30 days with a special offer email to encourage repeat purchases."}, "success": true}, {"input": {"insight": {"id": 39, "location_id": 1, "title": "Re-engagement Strategies for Inactive Customers", "description": "Implement re-engagement strategies for customers who have made purchases but have not returned recently. For instance, customers with one order in the last 30 days should receive targeted offers to entice them back. Define specific offers and track their effectiveness in driving repeat purchases.", "impact": "medium", "type": "campaign", "actions": ["Identify customers with one-time purchases", "Create tailored offers to incentivize return visits", "Track success rates of re-engagement campaigns"], "plan": {"name": "Re-engagement Campaign for Inactive Customers", "items": {"reengagementCampaign": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["inactiveCustomersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "reengagementTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Thank you for your recent purchase. To show our appreciation, here’s an exclusive 15% discount on your next order. Use code WELCOME15 at checkout. Hurry, offer valid for a limited time!</p><p>We look forward to seeing you again soon.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer <PERSON><PERSON>", "text": "We miss you! Thank you for your recent purchase. Enjoy 15% off your next order with code WELCOME15. Limited time offer. We look forward to your next visit. Best regards, Your Company Team", "editor": "code", "subject": "Special Offer Just for You - Come Back and Save!", "reply_to": "<EMAIL>"}, "name": "Re-engagement Offer <PERSON><PERSON>", "type": "email", "locale": "en", "campaign_ref": "reengagementCampaign"}}}, "inactiveCustomersList": {"type": "list", "config": {"list": {"name": "Inactive Customers Last 30 Days", "rule": {"path": "purchase_count_last_30_days", "type": "string", "uuid": "123e4567-e89b-12d3-a456-************", "group": "user", "value": "1", "children": [], "operator": "="}, "tags": ["customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with exactly one order in the last 30 days who have not returned recently."}}}}, "description": "Target customers who made a single purchase in the last 30 days with a special offer email to encourage repeat purchases."}, "status": "new", "created_at": "2025-06-16T11:43:00.000Z", "updated_at": "2025-06-16T12:21:26.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Re-engagement Campaign for Inactive Customers", "items": {"reengagementCampaign": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["inactiveCustomersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "reengagementTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Thank you for your recent purchase. To show our appreciation, here’s an exclusive 15% discount on your next order. Use code WELCOME15 at checkout. Hurry, offer valid for a limited time!</p><p>We look forward to seeing you again soon.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer <PERSON><PERSON>", "text": "We miss you! Thank you for your recent purchase. Enjoy 15% off your next order with code WELCOME15. Limited time offer. We look forward to your next visit. Best regards, Your Company Team", "editor": "code", "subject": "Special Offer Just for You - Come Back and Save!", "reply_to": "<EMAIL>"}, "name": "Re-engagement Offer <PERSON><PERSON>", "type": "email", "locale": "en", "campaign_ref": "reengagementCampaign"}}}, "inactiveCustomersList": {"type": "list", "config": {"list": {"name": "Inactive Customers Last 30 Days", "rule": {"path": "purchase_count_last_30_days", "type": "string", "uuid": "123e4567-e89b-12d3-a456-************", "group": "user", "value": "1", "children": [], "operator": "="}, "tags": ["customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with exactly one order in the last 30 days who have not returned recently."}}}}, "description": "Target customers who made a single purchase in the last 30 days with a special offer email to encourage repeat purchases."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}, {"id": 6, "name": "Inactive Customers Last 30 Days", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}, {"id": 9, "name": "Re-engagement Offer Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}, {"id": 8, "type": "email", "campaign_id": 9}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Re-engagement Campaign for Inactive Customers", "items": {"reengagementCampaign": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["inactiveCustomersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "reengagementTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Thank you for your recent purchase. To show our appreciation, here’s an exclusive 15% discount on your next order. Use code WELCOME15 at checkout. Hurry, offer valid for a limited time!</p><p>We look forward to seeing you again soon.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer <PERSON><PERSON>", "text": "We miss you! Thank you for your recent purchase. Enjoy 15% off your next order with code WELCOME15. Limited time offer. We look forward to your next visit. Best regards, Your Company Team", "editor": "code", "subject": "Special Offer Just for You - Come Back and Save!", "reply_to": "<EMAIL>"}, "name": "Re-engagement Offer <PERSON><PERSON>", "type": "email", "locale": "en", "campaign_ref": "reengagementCampaign"}}}, "inactiveCustomersList": {"type": "list", "config": {"list": {"name": "Inactive Customers Last 30 Days", "rule": {"path": "purchase_count_last_30_days", "type": "string", "uuid": "123e4567-e89b-12d3-a456-************", "group": "user", "value": "1", "children": [], "operator": "="}, "tags": ["customer-engagement"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with exactly one order in the last 30 days who have not returned recently."}}}}, "description": "Target customers who made a single purchase in the last 30 days with a special offer email to encourage repeat purchases."}, "success": true}, {"input": {"insight": {"id": 34, "location_id": 1, "title": "Personalized Recommendations for Frequent Buyers", "description": "For customers with multiple orders, like <PERSON> and <PERSON>, implement personalized product recommendations based on their previous purchases to enhance their shopping experience and increase sales significantly.", "impact": "high", "type": "automation", "actions": ["Utilize customer data to generate personalized recommendations", "Automate the delivery of these recommendations through email", "Monitor the effectiveness of personalized campaigns"], "plan": {"name": "Personalized Recommendations for Frequent Buyers", "items": {"frequentBuyersList": {"type": "list", "config": {"list": {"name": "Frequent Buyers", "rule": {"path": "orders.count", "type": "number", "uuid": "auto-generated-uuid-fb-001", "group": "user", "value": 2, "children": [], "operator": ">="}, "tags": ["customer-engagement", "cross-sell"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with multiple orders for personalized product recommendations."}}}, "personalizedRecommendationsJourney": {"type": "journey", "config": {"journey": {"name": "Frequent Buyer Personalized Recommendations Journey", "tags": ["customer-engagement", "cross-sell", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Start Journey - Frequent Buyers", "type": "entrance", "children": [{"external_id": "sendEmailStep"}]}, "sendEmailStep": {"x": 240, "y": 0, "data": {"campaign_ref": "personalizedRecommendationsCampaign"}, "name": "Send Personalized Recommendations Email", "type": "action", "children": []}}, "published": false, "description": "Journey to automatically send personalized product recommendations emails to frequent buyers to increase engagement and sales."}}}, "personalizedRecommendationsCampaign": {"type": "campaign", "config": {"campaign": {"name": "Personalized Recommendations Campaign", "tags": ["customer-engagement", "cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["frequentBuyersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "personalizedRecommendationsTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear {{user.first_name}},</p><p>Thank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.</p><ul>{{#each personalized_recommendations}}<li>{{this.product_name}} - <a href='{{this.product_url}}'>View Product</a></li>{{/each}}</ul><p>We look forward to enhancing your shopping experience!</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Personalized Recommendations Email Template", "text": "Dear {{user.first_name}},\n\nThank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.\n\n{{#each personalized_recommendations}}\n- {{this.product_name}}: {{this.product_url}}\n{{/each}}\n\nWe look forward to enhancing your shopping experience!\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "Exclusive Recommendations Just for You", "reply_to": "<EMAIL>"}, "name": "Personalized Recommendations Email Template", "type": "email", "locale": "en", "campaign_ref": "personalizedRecommendationsCampaign"}}}}, "description": "Automation plan to deliver personalized product recommendations via email to frequent buyers like <PERSON> and <PERSON>, enhancing their shopping experience while respecting privacy and brand standards."}, "status": "new", "created_at": "2025-06-14T15:31:54.000Z", "updated_at": "2025-06-16T12:30:16.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Personalized Recommendations for Frequent Buyers", "items": {"frequentBuyersList": {"type": "list", "config": {"list": {"name": "Frequent Buyers", "rule": {"path": "orders.count", "type": "number", "uuid": "auto-generated-uuid-fb-001", "group": "user", "value": 2, "children": [], "operator": ">="}, "tags": ["customer-engagement", "cross-sell"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with multiple orders for personalized product recommendations."}}}, "personalizedRecommendationsJourney": {"type": "journey", "config": {"journey": {"name": "Frequent Buyer Personalized Recommendations Journey", "tags": ["customer-engagement", "cross-sell", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Start Journey - Frequent Buyers", "type": "entrance", "children": [{"external_id": "sendEmailStep"}]}, "sendEmailStep": {"x": 240, "y": 0, "data": {"campaign_ref": "personalizedRecommendationsCampaign"}, "name": "Send Personalized Recommendations Email", "type": "action", "children": []}}, "published": false, "description": "Journey to automatically send personalized product recommendations emails to frequent buyers to increase engagement and sales."}}}, "personalizedRecommendationsCampaign": {"type": "campaign", "config": {"campaign": {"name": "Personalized Recommendations Campaign", "tags": ["customer-engagement", "cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["frequentBuyersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "personalizedRecommendationsTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear {{user.first_name}},</p><p>Thank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.</p><ul>{{#each personalized_recommendations}}<li>{{this.product_name}} - <a href='{{this.product_url}}'>View Product</a></li>{{/each}}</ul><p>We look forward to enhancing your shopping experience!</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Personalized Recommendations Email Template", "text": "Dear {{user.first_name}},\n\nThank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.\n\n{{#each personalized_recommendations}}\n- {{this.product_name}}: {{this.product_url}}\n{{/each}}\n\nWe look forward to enhancing your shopping experience!\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "Exclusive Recommendations Just for You", "reply_to": "<EMAIL>"}, "name": "Personalized Recommendations Email Template", "type": "email", "locale": "en", "campaign_ref": "personalizedRecommendationsCampaign"}}}}, "description": "Automation plan to deliver personalized product recommendations via email to frequent buyers like <PERSON> and <PERSON>, enhancing their shopping experience while respecting privacy and brand standards."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}, {"id": 6, "name": "Inactive Customers Last 30 Days", "tags": []}, {"id": 7, "name": "Frequent Buyers", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}, {"id": 9, "name": "Re-engagement Offer Campaign", "tags": []}, {"id": 10, "name": "Personalized Recommendations Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}, {"id": 8, "type": "email", "campaign_id": 9}, {"id": 9, "type": "email", "campaign_id": 10}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 3, "name": "Frequent Buyer Personalized Recommendations Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Personalized Recommendations for Frequent Buyers", "items": {"frequentBuyersList": {"type": "list", "config": {"list": {"name": "Frequent Buyers", "rule": {"path": "orders.count", "type": "number", "uuid": "auto-generated-uuid-fb-001", "group": "user", "value": 2, "children": [], "operator": ">="}, "tags": ["customer-engagement", "cross-sell"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with multiple orders for personalized product recommendations."}}}, "personalizedRecommendationsJourney": {"type": "journey", "config": {"journey": {"name": "Frequent Buyer Personalized Recommendations Journey", "tags": ["customer-engagement", "cross-sell", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Start Journey - Frequent Buyers", "type": "entrance", "children": [{"external_id": "sendEmailStep"}]}, "sendEmailStep": {"x": 240, "y": 0, "data": {"campaign_ref": "personalizedRecommendationsCampaign"}, "name": "Send Personalized Recommendations Email", "type": "action", "children": []}}, "published": false, "description": "Journey to automatically send personalized product recommendations emails to frequent buyers to increase engagement and sales."}}}, "personalizedRecommendationsCampaign": {"type": "campaign", "config": {"campaign": {"name": "Personalized Recommendations Campaign", "tags": ["customer-engagement", "cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["frequentBuyersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "personalizedRecommendationsTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear {{user.first_name}},</p><p>Thank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.</p><ul>{{#each personalized_recommendations}}<li>{{this.product_name}} - <a href='{{this.product_url}}'>View Product</a></li>{{/each}}</ul><p>We look forward to enhancing your shopping experience!</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Personalized Recommendations Email Template", "text": "Dear {{user.first_name}},\n\nThank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.\n\n{{#each personalized_recommendations}}\n- {{this.product_name}}: {{this.product_url}}\n{{/each}}\n\nWe look forward to enhancing your shopping experience!\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "Exclusive Recommendations Just for You", "reply_to": "<EMAIL>"}, "name": "Personalized Recommendations Email Template", "type": "email", "locale": "en", "campaign_ref": "personalizedRecommendationsCampaign"}}}}, "description": "Automation plan to deliver personalized product recommendations via email to frequent buyers like <PERSON> and <PERSON>, enhancing their shopping experience while respecting privacy and brand standards."}, "success": true}, {"input": {"insight": {"id": 34, "location_id": 1, "title": "Personalized Recommendations for Frequent Buyers", "description": "For customers with multiple orders, like <PERSON> and <PERSON>, implement personalized product recommendations based on their previous purchases to enhance their shopping experience and increase sales significantly.", "impact": "high", "type": "automation", "actions": ["Utilize customer data to generate personalized recommendations", "Automate the delivery of these recommendations through email", "Monitor the effectiveness of personalized campaigns"], "plan": {"name": "Personalized Recommendations for Frequent Buyers", "items": {"frequentBuyersList": {"type": "list", "config": {"list": {"name": "Frequent Buyers", "rule": {"path": "orders.count", "type": "number", "uuid": "auto-generated-uuid-fb-001", "group": "user", "value": 2, "children": [], "operator": ">="}, "tags": ["customer-engagement", "cross-sell"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with multiple orders for personalized product recommendations."}}}, "personalizedRecommendationsJourney": {"type": "journey", "config": {"journey": {"name": "Frequent Buyer Personalized Recommendations Journey", "tags": ["customer-engagement", "cross-sell", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Start Journey - Frequent Buyers", "type": "entrance", "children": [{"external_id": "sendEmailStep"}]}, "sendEmailStep": {"x": 240, "y": 0, "data": {"campaign_ref": "personalizedRecommendationsCampaign"}, "name": "Send Personalized Recommendations Email", "type": "action", "children": []}}, "published": false, "description": "Journey to automatically send personalized product recommendations emails to frequent buyers to increase engagement and sales."}}}, "personalizedRecommendationsCampaign": {"type": "campaign", "config": {"campaign": {"name": "Personalized Recommendations Campaign", "tags": ["customer-engagement", "cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["frequentBuyersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "personalizedRecommendationsTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear {{user.first_name}},</p><p>Thank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.</p><ul>{{#each personalized_recommendations}}<li>{{this.product_name}} - <a href='{{this.product_url}}'>View Product</a></li>{{/each}}</ul><p>We look forward to enhancing your shopping experience!</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Personalized Recommendations Email Template", "text": "Dear {{user.first_name}},\n\nThank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.\n\n{{#each personalized_recommendations}}\n- {{this.product_name}}: {{this.product_url}}\n{{/each}}\n\nWe look forward to enhancing your shopping experience!\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "Exclusive Recommendations Just for You", "reply_to": "<EMAIL>"}, "name": "Personalized Recommendations Email Template", "type": "email", "locale": "en", "campaign_ref": "personalizedRecommendationsCampaign"}}}}, "description": "Automation plan to deliver personalized product recommendations via email to frequent buyers like <PERSON> and <PERSON>, enhancing their shopping experience while respecting privacy and brand standards."}, "status": "new", "created_at": "2025-06-14T15:31:54.000Z", "updated_at": "2025-06-16T12:30:16.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Personalized Recommendations for Frequent Buyers", "items": {"frequentBuyersList": {"type": "list", "config": {"list": {"name": "Frequent Buyers", "rule": {"path": "orders.count", "type": "number", "uuid": "auto-generated-uuid-fb-001", "group": "user", "value": 2, "children": [], "operator": ">="}, "tags": ["customer-engagement", "cross-sell"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with multiple orders for personalized product recommendations."}}}, "personalizedRecommendationsJourney": {"type": "journey", "config": {"journey": {"name": "Frequent Buyer Personalized Recommendations Journey", "tags": ["customer-engagement", "cross-sell", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Start Journey - Frequent Buyers", "type": "entrance", "children": [{"external_id": "sendEmailStep"}]}, "sendEmailStep": {"x": 240, "y": 0, "data": {"campaign_ref": "personalizedRecommendationsCampaign"}, "name": "Send Personalized Recommendations Email", "type": "action", "children": []}}, "published": false, "description": "Journey to automatically send personalized product recommendations emails to frequent buyers to increase engagement and sales."}}}, "personalizedRecommendationsCampaign": {"type": "campaign", "config": {"campaign": {"name": "Personalized Recommendations Campaign", "tags": ["customer-engagement", "cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["frequentBuyersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "personalizedRecommendationsTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear {{user.first_name}},</p><p>Thank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.</p><ul>{{#each personalized_recommendations}}<li>{{this.product_name}} - <a href='{{this.product_url}}'>View Product</a></li>{{/each}}</ul><p>We look forward to enhancing your shopping experience!</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Personalized Recommendations Email Template", "text": "Dear {{user.first_name}},\n\nThank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.\n\n{{#each personalized_recommendations}}\n- {{this.product_name}}: {{this.product_url}}\n{{/each}}\n\nWe look forward to enhancing your shopping experience!\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "Exclusive Recommendations Just for You", "reply_to": "<EMAIL>"}, "name": "Personalized Recommendations Email Template", "type": "email", "locale": "en", "campaign_ref": "personalizedRecommendationsCampaign"}}}}, "description": "Automation plan to deliver personalized product recommendations via email to frequent buyers like <PERSON> and <PERSON>, enhancing their shopping experience while respecting privacy and brand standards."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}, {"id": 6, "name": "Inactive Customers Last 30 Days", "tags": []}, {"id": 7, "name": "Frequent Buyers", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}, {"id": 9, "name": "Re-engagement Offer Campaign", "tags": []}, {"id": 10, "name": "Personalized Recommendations Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}, {"id": 8, "type": "email", "campaign_id": 9}, {"id": 9, "type": "email", "campaign_id": 10}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 3, "name": "Frequent Buyer Personalized Recommendations Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Personalized Recommendations for Frequent Buyers", "items": {"frequentBuyersList": {"type": "list", "config": {"list": {"name": "Frequent Buyers", "rule": {"path": "orders.count", "type": "number", "uuid": "auto-generated-uuid-fb-001", "group": "user", "value": 2, "children": [], "operator": ">="}, "tags": ["customer-engagement", "cross-sell"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with multiple orders for personalized product recommendations."}}}, "personalizedRecommendationsJourney": {"type": "journey", "config": {"journey": {"name": "Frequent Buyer Personalized Recommendations Journey", "tags": ["customer-engagement", "cross-sell", "automation"], "steps": {"entrance_step": {"x": 0, "y": 0, "data": {"trigger": "schedule"}, "name": "Start Journey - Frequent Buyers", "type": "entrance", "children": [{"external_id": "sendEmailStep"}]}, "sendEmailStep": {"x": 240, "y": 0, "data": {"campaign_ref": "personalizedRecommendationsCampaign"}, "name": "Send Personalized Recommendations Email", "type": "action", "children": []}}, "published": false, "description": "Journey to automatically send personalized product recommendations emails to frequent buyers to increase engagement and sales."}}}, "personalizedRecommendationsCampaign": {"type": "campaign", "config": {"campaign": {"name": "Personalized Recommendations Campaign", "tags": ["customer-engagement", "cross-sell", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["frequentBuyersList"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "personalizedRecommendationsTemplate": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><p>Dear {{user.first_name}},</p><p>Thank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.</p><ul>{{#each personalized_recommendations}}<li>{{this.product_name}} - <a href='{{this.product_url}}'>View Product</a></li>{{/each}}</ul><p>We look forward to enhancing your shopping experience!</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Personalized Recommendations Email Template", "text": "Dear {{user.first_name}},\n\nThank you for being a valued customer. Based on your previous purchases, we have handpicked some products that we think you'll love.\n\n{{#each personalized_recommendations}}\n- {{this.product_name}}: {{this.product_url}}\n{{/each}}\n\nWe look forward to enhancing your shopping experience!\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "Exclusive Recommendations Just for You", "reply_to": "<EMAIL>"}, "name": "Personalized Recommendations Email Template", "type": "email", "locale": "en", "campaign_ref": "personalizedRecommendationsCampaign"}}}}, "description": "Automation plan to deliver personalized product recommendations via email to frequent buyers like <PERSON> and <PERSON>, enhancing their shopping experience while respecting privacy and brand standards."}, "success": true}, {"input": {"insight": {"id": 37, "location_id": 1, "title": "Segmented Marketing Campaigns", "description": "Leverage customer segmentation to create tailored marketing campaigns. For example, customers with low order frequency but high purchase amounts may respond well to re-engagement campaigns. Identify specific segments and create targeted messaging for each.", "impact": "medium", "type": "campaign", "actions": ["Analyze customer segments based on order frequency and value", "Design targeted re-engagement campaigns for each segment", "Evaluate the effectiveness of the campaigns"], "plan": {"name": "Re-engagement Campaign for High Value Low Frequency Customers", "items": {"listHighAOVLowFreq": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "id", "type": "string", "uuid": "auto-generated-uuid", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["customer-engagement", "automation"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value but low purchase frequency, targeted for re-engagement."}}}, "campaignReengagement": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVLowFreq"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateReengagement": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Dear Valued Customer,</p><p>It's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.</p><p><a href='https://www.example.com/reengage'>Claim Your Offer</a></p><p>Thank you for being a part of our community.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer Template", "text": "We Miss You!\n\nDear Valued Customer,\n\nIt's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.\n\nClaim Your Offer: https://www.example.com/reengage\n\nThank you for being a part of our community.\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "We Miss You – Exclusive Offer Just for You", "reply_to": "<EMAIL>", "preheader": ""}, "name": "Re-engagement Offer Template", "type": "email", "locale": "en", "campaign_ref": "campaignReengagement"}}}}, "description": "A targeted email blast campaign to re-engage customers with low order frequency but high average order value, encouraging repeat purchases with a professional and brand-appropriate message."}, "status": "new", "created_at": "2025-06-16T11:43:00.000Z", "updated_at": "2025-06-16T16:44:08.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Re-engagement Campaign for High Value Low Frequency Customers", "items": {"listHighAOVLowFreq": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "id", "type": "string", "uuid": "auto-generated-uuid", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["customer-engagement", "automation"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value but low purchase frequency, targeted for re-engagement."}}}, "campaignReengagement": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVLowFreq"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateReengagement": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Dear Valued Customer,</p><p>It's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.</p><p><a href='https://www.example.com/reengage'>Claim Your Offer</a></p><p>Thank you for being a part of our community.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer Template", "text": "We Miss You!\n\nDear Valued Customer,\n\nIt's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.\n\nClaim Your Offer: https://www.example.com/reengage\n\nThank you for being a part of our community.\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "We Miss You – Exclusive Offer Just for You", "reply_to": "<EMAIL>", "preheader": ""}, "name": "Re-engagement Offer Template", "type": "email", "locale": "en", "campaign_ref": "campaignReengagement"}}}}, "description": "A targeted email blast campaign to re-engage customers with low order frequency but high average order value, encouraging repeat purchases with a professional and brand-appropriate message."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}, {"id": 6, "name": "Inactive Customers Last 30 Days", "tags": []}, {"id": 7, "name": "Frequent Buyers", "tags": []}, {"id": 8, "name": "High AOV Target Customers", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}, {"id": 9, "name": "Re-engagement Offer Campaign", "tags": []}, {"id": 10, "name": "Personalized Recommendations Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}, {"id": 8, "type": "email", "campaign_id": 9}, {"id": 9, "type": "email", "campaign_id": 10}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 3, "name": "Frequent Buyer Personalized Recommendations Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Re-engagement Campaign for High Value Low Frequency Customers", "items": {"listHighAOVLowFreq": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "id", "type": "string", "uuid": "auto-generated-uuid", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["customer-engagement", "automation"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value but low purchase frequency, targeted for re-engagement."}}}, "campaignReengagement": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVLowFreq"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateReengagement": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Dear Valued Customer,</p><p>It's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.</p><p><a href='https://www.example.com/reengage'>Claim Your Offer</a></p><p>Thank you for being a part of our community.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer Template", "text": "We Miss You!\n\nDear Valued Customer,\n\nIt's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.\n\nClaim Your Offer: https://www.example.com/reengage\n\nThank you for being a part of our community.\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "We Miss You – Exclusive Offer Just for You", "reply_to": "<EMAIL>", "preheader": ""}, "name": "Re-engagement Offer Template", "type": "email", "locale": "en", "campaign_ref": "campaignReengagement"}}}}, "description": "A targeted email blast campaign to re-engage customers with low order frequency but high average order value, encouraging repeat purchases with a professional and brand-appropriate message."}, "success": true}, {"input": {"insight": {"id": 37, "location_id": 1, "title": "Segmented Marketing Campaigns", "description": "Leverage customer segmentation to create tailored marketing campaigns. For example, customers with low order frequency but high purchase amounts may respond well to re-engagement campaigns. Identify specific segments and create targeted messaging for each.", "impact": "medium", "type": "campaign", "actions": ["Analyze customer segments based on order frequency and value", "Design targeted re-engagement campaigns for each segment", "Evaluate the effectiveness of the campaigns"], "plan": {"name": "Re-engagement Campaign for High Value Low Frequency Customers", "items": {"listHighAOVLowFreq": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "id", "type": "string", "uuid": "auto-generated-uuid", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["customer-engagement", "automation"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value but low purchase frequency, targeted for re-engagement."}}}, "campaignReengagement": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVLowFreq"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateReengagement": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Dear Valued Customer,</p><p>It's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.</p><p><a href='https://www.example.com/reengage'>Claim Your Offer</a></p><p>Thank you for being a part of our community.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer Template", "text": "We Miss You!\n\nDear Valued Customer,\n\nIt's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.\n\nClaim Your Offer: https://www.example.com/reengage\n\nThank you for being a part of our community.\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "We Miss You – Exclusive Offer Just for You", "reply_to": "<EMAIL>", "preheader": ""}, "name": "Re-engagement Offer Template", "type": "email", "locale": "en", "campaign_ref": "campaignReengagement"}}}}, "description": "A targeted email blast campaign to re-engage customers with low order frequency but high average order value, encouraging repeat purchases with a professional and brand-appropriate message."}, "status": "new", "created_at": "2025-06-16T11:43:00.000Z", "updated_at": "2025-06-16T16:44:08.000Z", "acted_at": null, "execution_results": null, "executed_at": null, "delivery_channel": "email", "agent_id": null, "agent_name": null}, "plan": {"name": "Re-engagement Campaign for High Value Low Frequency Customers", "items": {"listHighAOVLowFreq": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "id", "type": "string", "uuid": "auto-generated-uuid", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["customer-engagement", "automation"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value but low purchase frequency, targeted for re-engagement."}}}, "campaignReengagement": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVLowFreq"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateReengagement": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Dear Valued Customer,</p><p>It's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.</p><p><a href='https://www.example.com/reengage'>Claim Your Offer</a></p><p>Thank you for being a part of our community.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer Template", "text": "We Miss You!\n\nDear Valued Customer,\n\nIt's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.\n\nClaim Your Offer: https://www.example.com/reengage\n\nThank you for being a part of our community.\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "We Miss You – Exclusive Offer Just for You", "reply_to": "<EMAIL>", "preheader": ""}, "name": "Re-engagement Offer Template", "type": "email", "locale": "en", "campaign_ref": "campaignReengagement"}}}}, "description": "A targeted email blast campaign to re-engage customers with low order frequency but high average order value, encouraging repeat purchases with a professional and brand-appropriate message."}, "resources": {"tags": ["cross-sell", "customer-engagement", "automation"], "lists": [{"id": 1, "name": "Target Customers", "tags": []}, {"id": 2, "name": "High AOV Target Customers", "tags": []}, {"id": 3, "name": "High AOV Target Customers", "tags": []}, {"id": 4, "name": "Target Customers", "tags": []}, {"id": 5, "name": "High AOV Target Customers", "tags": []}, {"id": 6, "name": "Inactive Customers Last 30 Days", "tags": []}, {"id": 7, "name": "Frequent Buyers", "tags": []}, {"id": 8, "name": "High AOV Target Customers", "tags": []}], "campaigns": [{"id": 1, "name": "Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 2, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 3, "name": "Worker Test Campaign", "tags": []}, {"id": 4, "name": "Worker Test Campaign", "tags": []}, {"id": 5, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 6, "name": "Copy of Analyze Customer Churn Risk Campaign", "tags": []}, {"id": 7, "name": "High AOV Product Promotion Campaign", "tags": []}, {"id": 8, "name": "Cross-Sell Complementary Products Campaign", "tags": []}, {"id": 9, "name": "Re-engagement Offer Campaign", "tags": []}, {"id": 10, "name": "Personalized Recommendations Campaign", "tags": []}], "templates": [{"id": 1, "type": "email", "campaign_id": 1}, {"id": 2, "type": "email", "campaign_id": 2}, {"id": 3, "type": null, "campaign_id": 4}, {"id": 4, "type": "email", "campaign_id": 5}, {"id": 5, "type": "email", "campaign_id": 6}, {"id": 6, "type": "email", "campaign_id": 7}, {"id": 7, "type": "email", "campaign_id": 8}, {"id": 8, "type": "email", "campaign_id": 9}, {"id": 9, "type": "email", "campaign_id": 10}], "journeys": [{"id": 1, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 2, "name": "Cross-Selling Customer Journey", "tags": []}, {"id": 3, "name": "Frequent Buyer Personalized Recommendations Journey", "tags": []}]}, "subscriptions": {"email": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "<PERSON><PERSON><PERSON>", "channel": "email", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}, "text": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "location_id": 1, "name": "Default SMS", "channel": "text", "from_name": null, "from_email": null, "reply_to": null, "cc": null, "bcc": null, "from_phone": null}}, "providers": {"email": {"id": 2, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-13T18:03:53.000Z", "type": "sendgrid", "name": "Sendgrid", "location_id": 1, "group": "email", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}, "text": {"id": 1, "created_at": "2025-06-13T18:03:52.000Z", "updated_at": "2025-06-14T12:14:18.000Z", "type": "sendgrid", "name": "SendGrid Email Provider", "location_id": 1, "group": "text", "data": {"api_key": "*********************************************************************"}, "is_default": false, "rate_limit": 12, "rate_interval": "second", "api_key": "*********************************************************************"}}}, "output": {"name": "Re-engagement Campaign for High Value Low Frequency Customers", "items": {"listHighAOVLowFreq": {"type": "list", "config": {"list": {"name": "High AOV Target Customers", "rule": {"path": "id", "type": "string", "uuid": "auto-generated-uuid", "group": "user", "value": "", "children": [], "operator": "is set"}, "tags": ["customer-engagement", "automation"], "type": "dynamic", "user_ids": [], "is_visible": true, "description": "Customers with high average order value but low purchase frequency, targeted for re-engagement."}}}, "campaignReengagement": {"type": "campaign", "config": {"campaign": {"name": "Re-engagement Offer Campaign", "tags": ["customer-engagement", "automation"], "type": "blast", "channel": "email", "send_at": null, "list_refs": ["listHighAOVLowFreq"], "provider_id": 2, "subscription_id": 1, "send_in_user_timezone": true}}}, "templateReengagement": {"type": "template", "config": {"template": {"data": {"from": {"name": "Zen Leaf", "address": "<EMAIL>"}, "html": "<html><body><h1>We Miss You!</h1><p>Dear Valued Customer,</p><p>It's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.</p><p><a href='https://www.example.com/reengage'>Claim Your Offer</a></p><p>Thank you for being a part of our community.</p><p>Best regards,<br/>Your Company Team</p></body></html>", "name": "Re-engagement Offer Template", "text": "We Miss You!\n\nDear Valued Customer,\n\nIt's been a while since your last purchase. We appreciate your loyalty and high-value orders. To welcome you back, enjoy an exclusive offer tailored just for you.\n\nClaim Your Offer: https://www.example.com/reengage\n\nThank you for being a part of our community.\n\nBest regards,\nYour Company Team", "editor": "code", "subject": "We Miss You – Exclusive Offer Just for You", "reply_to": "<EMAIL>", "preheader": ""}, "name": "Re-engagement Offer Template", "type": "email", "locale": "en", "campaign_ref": "campaignReengagement"}}}}, "description": "A targeted email blast campaign to re-engage customers with low order frequency but high average order value, encouraging repeat purchases with a professional and brand-appropriate message."}, "success": true}], "error_patterns": []}