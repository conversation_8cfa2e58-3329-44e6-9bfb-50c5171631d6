import { logger } from "../config/logger";
import { loadEmailChannel } from "../providers/email";
import { Email } from "../providers/email/Email";
import Admin from "./Admin";
import Organization from "../organizations/Organization";
import Location from "../locations/Location";
import App from "../app";

export interface AdminInvitationParams {
  admin: Admin;
  organization: Organization;
  invitedBy: Admin;
  location?: Location; // Optional for location-specific invitations
}

export class AdminInvitationService {
  /**
   * Send invitation email to a newly created admin
   */
  static async sendInvitationEmail(params: AdminInvitationParams): Promise<boolean> {
    try {
      const { admin, organization, invitedBy, location } = params;
      
      logger.info(`Sending invitation email to admin ${admin.email} for organization ${organization.id}`);

      // Get the first available email provider for the organization
      // We'll use the first location's email provider or a global one
      const emailProvider = await this.getEmailProvider(organization, location);
      
      if (!emailProvider) {
        logger.warn(`No email provider available for organization ${organization.id}`);
        return false;
      }

      // Generate the invitation email
      const email = this.generateInvitationEmail({
        admin,
        organization,
        invitedBy,
        location,
      });

      // Send the email
      await emailProvider.provider.send(email);
      
      logger.info(`Successfully sent invitation email to ${admin.email}`);
      return true;
    } catch (error) {
      logger.error(`Failed to send invitation email to ${params.admin.email}:`, error);
      return false;
    }
  }

  /**
   * Get an available email provider for sending system emails
   */
  private static async getEmailProvider(organization: Organization, location?: Location) {
    try {
      // If a specific location is provided, try to use its email provider
      if (location) {
        const locationProviders = await this.getLocationEmailProviders(location.id);
        if (locationProviders.length > 0) {
          return await loadEmailChannel(locationProviders[0].id, location.id);
        }
      }

      // Otherwise, try to get any email provider from the organization's locations
      const orgLocations = await Location.all((qb) => 
        qb.where('organization_id', organization.id).limit(10)
      );

      for (const loc of orgLocations) {
        const providers = await this.getLocationEmailProviders(loc.id);
        if (providers.length > 0) {
          const channel = await loadEmailChannel(providers[0].id, loc.id);
          if (channel) return channel;
        }
      }

      return null;
    } catch (error) {
      logger.error('Error getting email provider:', error);
      return null;
    }
  }

  /**
   * Get email providers for a location
   */
  private static async getLocationEmailProviders(locationId: number) {
    const { default: Provider } = await import('../providers/Provider');
    return await Provider.all((qb) => 
      qb.where('location_id', locationId)
        .where('group', 'email')
        .where('is_default', true)
        .orderBy('created_at', 'desc')
    );
  }

  /**
   * Generate the invitation email content
   */
  private static generateInvitationEmail(params: AdminInvitationParams): Email {
    const { admin, organization, invitedBy, location } = params;
    
    const baseUrl = App.main.env.baseUrl || 'https://app.bakedbot.ai';
    const loginUrl = `${baseUrl}/auth/login`;
    
    const subject = location 
      ? `You've been invited to ${location.name} on BakedBot`
      : `You've been invited to ${organization.name} on BakedBot`;

    const htmlContent = this.generateHtmlTemplate({
      admin,
      organization,
      invitedBy,
      location,
      loginUrl,
    });

    const textContent = this.generateTextTemplate({
      admin,
      organization,
      invitedBy,
      location,
      loginUrl,
    });

    return {
      to: admin.email,
      from: '<EMAIL>',
      subject,
      html: htmlContent,
      text: textContent,
    };
  }

  /**
   * Generate HTML email template
   */
  private static generateHtmlTemplate(params: {
    admin: Admin;
    organization: Organization;
    invitedBy: Admin;
    location?: Location;
    loginUrl: string;
  }): string {
    const { admin, organization, invitedBy, location, loginUrl } = params;

    const greeting = admin.first_name ? `Hi ${admin.first_name}` : 'Hello';
    const inviterName = invitedBy.first_name && invitedBy.last_name
      ? `${invitedBy.first_name} ${invitedBy.last_name}`
      : invitedBy.email;

    const contextText = location
      ? `You've been invited to join the team at <strong>${location.name}</strong> on BakedBot.`
      : `You've been invited to join <strong>${organization.username}</strong> on BakedBot.`;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>BakedBot Invitation</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
          .content { background: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
          .footer { text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">🤖 BakedBot</div>
          </div>

          <div class="content">
            <h2>${greeting}!</h2>

            <p>${contextText}</p>

            <p><strong>${inviterName}</strong> has added you as an admin and you now have access to manage campaigns, analyze customer data, and grow the business.</p>

            <p>To get started, simply click the button below to log in to your account:</p>

            <p style="text-align: center; margin: 30px 0;">
              <a href="${loginUrl}" class="button">Access Your Account</a>
            </p>

            <p>If you have any questions, feel free to reach out to ${inviterName} at ${invitedBy.email}.</p>

            <p>Welcome to the team!</p>
          </div>

          <div class="footer">
            <p>This invitation was sent by ${organization.username} via BakedBot</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate plain text email template
   */
  private static generateTextTemplate(params: {
    admin: Admin;
    organization: Organization;
    invitedBy: Admin;
    location?: Location;
    loginUrl: string;
  }): string {
    const { admin, organization, invitedBy, location, loginUrl } = params;

    const greeting = admin.first_name ? `Hi ${admin.first_name}` : 'Hello';
    const inviterName = invitedBy.first_name && invitedBy.last_name
      ? `${invitedBy.first_name} ${invitedBy.last_name}`
      : invitedBy.email;

    const contextText = location
      ? `You've been invited to join the team at ${location.name} on BakedBot.`
      : `You've been invited to join ${organization.username} on BakedBot.`;

    return `
${greeting}!

${contextText}

${inviterName} has added you as an admin and you now have access to manage campaigns, analyze customer data, and grow the business.

To get started, simply visit the link below to log in to your account:

${loginUrl}

If you have any questions, feel free to reach out to ${inviterName} at ${invitedBy.email}.

Welcome to the team!

---
This invitation was sent by ${organization.username} via BakedBot.
If you didn't expect this invitation, you can safely ignore this email.
    `.trim();
  }
}
