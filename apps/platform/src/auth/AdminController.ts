import Router from "@koa/router";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { createOrUpdateAdmin, getAdmin, pagedAdmins } from "./AdminRepository";
import { AuthState } from "./AuthMiddleware";
import { Context } from "koa";
import {
  getOrganization,
  organizationRoleMiddleware,
  requireOrganizationRole,
} from "../organizations/OrganizationService";
import Admin from "./Admin";
import { organizationRoles } from "../organizations/Organization";
import { JSONSchemaType, validate } from "../core/validate";
import { AdminInvitationService } from "./AdminInvitationService";
import { logger } from "../config/logger";

/**
 * @swagger
 * components:
 *   schemas:
 *     Admin:
 *       type: object
 *       required: [id, organization_id, email, role]
 *       properties:
 *         id:
 *           type: number
 *         organization_id:
 *           type: number
 *         email:
 *           type: string
 *           format: email
 *         role:
 *           type: string
 *           enum: [admin, editor, viewer]
 *         first_name:
 *           type: string
 *           nullable: true
 *         last_name:
 *           type: string
 *           nullable: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * tags:
 *   name: Organization Admins
 *   description: Organization admin management endpoints
 */

const router = new Router<AuthState & { modelAdmin?: Admin }>({
  prefix: "/organizations/admins",
});

router.use(async (ctx: Context, next: () => void) => {
  ctx.state.organization = await getOrganization(
    ctx.state.admin.organization_id
  );
  return next();
});

router.use(organizationRoleMiddleware("admin"));

/**
 * @swagger
 * /organizations/admins:
 *   get:
 *     summary: Get paged list of organization admins
 *     tags: [Organization Admins]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of organization admins
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Admin'
 *                 total:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const organizationId = ctx.state.admin!.organization_id;
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedAdmins(organizationId, params);
});

const orgAdminParamsSchema: JSONSchemaType<
  Pick<Admin, "email" | "role" | "first_name" | "last_name">
> = {
  $id: "orgAdminParams",
  type: "object",
  required: ["role", "email"],
  properties: {
    email: {
      type: "string",
      format: "email",
    },
    role: {
      type: "string",
      enum: organizationRoles,
    },
    first_name: { type: "string", nullable: true },
    last_name: { type: "string", nullable: true },
  },
};

/**
 * @swagger
 * /organizations/admins:
 *   post:
 *     summary: Create a new organization admin
 *     tags: [Organization Admins]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [role, email]
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               role:
 *                 type: string
 *                 enum: [admin, editor, viewer]
 *               first_name:
 *                 type: string
 *                 nullable: true
 *               last_name:
 *                 type: string
 *                 nullable: true
 *     responses:
 *       200:
 *         description: Created admin
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Admin'
 */
router.post("/", async (ctx) => {
  const { role, email, first_name, last_name } = validate(
    orgAdminParamsSchema,
    ctx.request.body
  );

  // Ensure you dont add someone above your own role
  requireOrganizationRole(ctx.state.admin!, role);

  const newAdmin = await createOrUpdateAdmin({
    organization_id: ctx.state.admin!.organization_id,
    email,
    role,
    first_name,
    last_name,
  });

  // Send invitation email to the newly created admin
  try {
    await AdminInvitationService.sendInvitationEmail({
      admin: newAdmin,
      organization: ctx.state.organization,
      invitedBy: ctx.state.admin!,
    });
    logger.info(`Invitation email sent to new admin: ${newAdmin.email}`);
  } catch (error) {
    logger.error(`Failed to send invitation email to ${newAdmin.email}:`, error);
    // Don't fail the admin creation if email sending fails
  }

  ctx.body = newAdmin;
});

router.param("adminId", async (value, ctx, next) => {
  ctx.state.modelAdmin = await getAdmin(
    parseInt(value, 10),
    ctx.state.admin!.organization_id
  );
  if (!ctx.state.modelAdmin) {
    ctx.throw(404);
    return;
  }
  return await next();
});

/**
 * @swagger
 * /organizations/admins/{adminId}:
 *   get:
 *     summary: Get a specific organization admin
 *     tags: [Organization Admins]
 *     parameters:
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID
 *     responses:
 *       200:
 *         description: Admin details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Admin'
 *       404:
 *         description: Admin not found
 */
router.get("/:adminId", async (ctx) => {
  ctx.body = ctx.state.modelAdmin;
});

/**
 * @swagger
 * /organizations/admins/{adminId}:
 *   patch:
 *     summary: Update an organization admin
 *     tags: [Organization Admins]
 *     parameters:
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [role, email]
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               role:
 *                 type: string
 *                 enum: [admin, editor, viewer]
 *               first_name:
 *                 type: string
 *                 nullable: true
 *               last_name:
 *                 type: string
 *                 nullable: true
 *     responses:
 *       200:
 *         description: Updated admin
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Admin'
 *       404:
 *         description: Admin not found
 */
router.patch("/:adminId", async (ctx) => {
  const { role, email, first_name, last_name } = validate(
    orgAdminParamsSchema,
    ctx.request.body
  );

  // Ensure you dont add someone above your own role
  requireOrganizationRole(ctx.state.admin!, role);

  // Update the user with correct details
  ctx.body = Admin.updateAndFetch(ctx.state.modelAdmin!.id, {
    role,
    email,
    first_name,
    last_name,
  });
});

/**
 * @swagger
 * /organizations/admins/{adminId}:
 *   delete:
 *     summary: Delete an organization admin
 *     tags: [Organization Admins]
 *     parameters:
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID
 *     responses:
 *       200:
 *         description: Admin deleted successfully
 *       404:
 *         description: Admin not found
 */
router.delete("/:adminId", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, ctx.state.modelAdmin!.role);
  await Admin.deleteById(ctx.state.modelAdmin!.id);

  ctx.body = true;
});

export default router;
