import { OpenAI } from "openai";
import {
  Chat,
  Message,
  Agent,
  IntentAnalysis,
  AgentsConfig,
  AgentConfig,
  ChatInsight,
} from "./models/types";
import { logger } from "../config/logger";
import * as agentsConfig from "../agents/agents.json";
import { Knex } from "knex";
import { RetailerDataVectorService } from "../retailers/RetailerDataVectorService";
import { PosDataVectorService } from "../pos/PosDataVectorService";
import { ProductDataVectorService } from "../products/ProductDataVectorService";
import { ReviewDataVectorService } from "../reviews/ReviewDataVectorService";
import { CompetitorDataVectorService } from "../competitors/CompetitorDataVectorService";
import { CompetitorAnalysisService } from "../competitors/CompetitorAnalysisService";
import { Product } from "../products/Product";
import { AgentAvailabilityService } from "../agents/AgentAvailabilityService";
import { Document } from "../documents/Document";
import {
  VectorService,
  VectorData,
  VectorQueryResult,
} from "../core/VectorService";

// Interface for product data returned from vector search
interface ProductVectorResult {
  id?: string | number;
  product_id?: string | number;
  product_name?: string;
  description?: string;
  category?: string;
  price?: number;
  latest_price?: number;
  brand_name?: string;
  thc_percentage?: string | number;
  cbd_percentage?: string | number;
  image_url?: string;
  weight?: string;
  display_weight?: string;
  score?: number;
  metadata?: any;
  [key: string]: any;
}

// --- Tool Interfaces ---
interface ToolCall {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string; // JSON string of arguments
  };
}

interface ToolResultMessage {
  role: "tool";
  tool_call_id: string;
  name: string;
  content: string; // JSON string of the tool's result
}

// --- Data Aggregation Tool ---
type AggregationMetric =
  | "total_sales"
  | "total_profit"
  | "transaction_count"
  | "average_transaction_value"
  | "top_products_by_revenue"
  | "top_products_by_quantity"
  | "top_products_by_profit"
  | "profit_margin"
  | "traffic_by_day";

interface DataAggregationParams {
  metric: AggregationMetric;
  location_id: number;
  time_period?:
    | "last_month"
    | "this_month"
    | "last_30_days"
    | "last_year"
    | "this_year"
    | "ytd"
    | string;
  filters?: {
    category?: string;
    product_name?: string;
    customer_type?: string;
  };
  group_by?: "category" | "product_name" | "day_of_week" | "month";
  limit?: number;
}

// --- Vector Search Tool ---
interface VectorSearchParams {
  query: string;
  location_id: number;
  data_types: Array<
    | "pos_data"
    | "product_data"
    | "review_data"
    | "customer_data"
    | "retailer_data"
    | "competitor_data"
  >;
  limit?: number;
}

// --- Product Search Tool ---
interface ProductSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Review Search Tool ---
interface ReviewSearchParams {
  query: string;
  limit?: number;
}

// --- Competitor Search Tool ---
interface CompetitorSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Retailer Search Tool ---
interface RetailerSearchParams {
  query: string;
  limit?: number;
}

// --- POS Data Search Tool ---
interface PosDataSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Insights Search Tool ---
interface InsightsSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Customer Search Tool ---
interface CustomerSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Campaign Search Tool ---
interface CampaignSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- List Segment Search Tool ---
interface ListSegmentSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Automation Search Tool ---
interface AutomationSearchParams {
  query: string;
  location_id: number;
  limit?: number;
}

// --- Product Stats Tool ---
interface ProductStatsParams {
  location_id: number;
  filter_by?: {
    category?: string;
    brand_name?: string;
    price_range?: {
      min?: number;
      max?: number;
    };
  };
}

// --- Product By Name Tool ---
interface ProductByNameParams {
  product_name: string;
  location_id: number;
  exact_match?: boolean;
}

/**
 * ChatAIServiceWithTools
 *
 * A comprehensive chat AI service with tool calling capabilities
 * for hybrid RAG and structured data querying.
 */
export class ChatAIServiceWithTools {
  // Properties from ChatAIService
  private openai: OpenAI;
  private config: AgentsConfig;
  private contextWindow: number = 10;
  private defaultModel: string = "gpt-4.1-mini";
  private defaultTemperature: number = 0.7;
  private defaultMaxTokens: number = 32768;

  // Agent selection weights from ChatAIService
  private static CAPABILITY_MATCH_WEIGHT = 2.0;
  private static SPECIALIZATION_WEIGHT = 1.5;
  private static PREVIOUS_SUCCESS_WEIGHT = 1.0;
  private static MIN_CONFIDENCE_THRESHOLD = 0.6;

  // ChatAIServiceWithTools properties
  private db: Knex;
  private agentsConfig: AgentsConfig = agentsConfig as unknown as AgentsConfig;
  private currentUserId?: number;
  private usingCustomPrompt: boolean = false; // Track if we're using a custom prompt

  constructor(
    openai: OpenAI,
    db: Knex,
    options?: {
      defaultModel?: string;
      defaultTemperature?: number;
      defaultMaxTokens?: number;
      contextWindow?: number;
    }
  ) {
    this.openai = openai;
    this.config = agentsConfig as unknown as AgentsConfig;
    this.db = db;
    this.contextWindow = options?.contextWindow || 10;
    this.defaultModel = options?.defaultModel || "gpt-4.1-mini";
    this.defaultTemperature = options?.defaultTemperature || 0.7;
    this.defaultMaxTokens = options?.defaultMaxTokens || 32768;

    logger.info(
      {
        model: this.defaultModel,
      },
      "ChatAIServiceWithTools initialized"
    );
  }

  /**
   * Override the generateResponse method to use tool calling
   */
  async generateResponse(
    chat: Chat,
    message: Message,
    agent: Agent
  ): Promise<string> {
    const MAX_TOOL_ITERATIONS = 3;
    let iteration = 0;

    try {
      // Get agent configuration from our local copy
      const agentConfig = this.agentsConfig.agents[agent.id.toString()];
      if (!agentConfig) {
        throw new Error(`Agent configuration not found for ID: ${agent.id}`);
      }

      // Check if this is Smokey and we're processing a product-related query
      const isSmokey = agent.id === "smokey";
      const isProductQuery = this.isProductRecommendationQuery(message.content);

      // If this is Smokey handling a product query, we'll use a specialized approach
      if (isSmokey && isProductQuery) {
        const response = await this.generateProductRecommendation(
          chat,
          message,
          agent
        );
        return response;
      }

      // Check for intent in message metadata or perform intent analysis
      let intentAnalysis: IntentAnalysis;
      if (
        message.metadata?.intent &&
        message.metadata?.confidence &&
        typeof message.metadata?.intent === "string"
      ) {
        // Use existing intent analysis from metadata
        intentAnalysis = {
          intent: message.metadata.intent as string,
          confidence: message.metadata.confidence as number,
          entities: {
            agentType:
              (message.metadata.agentType as string) ||
              agent.name.toUpperCase(),
            requiredContextTypes:
              (message.metadata.requiredContextTypes as string[]) || [],
            needsDocumentSearch:
              (message.metadata.needsDocumentSearch as boolean) || false,
            mentionedAgent: message.metadata.mentionedAgent as
              | string
              | undefined,
          },
        };

        if (message.metadata?.mentionedAgent) {
          intentAnalysis.entities.mentionedAgent = message.metadata
            .mentionedAgent as string;
        }

        logger.info({
          message: "Using intent analysis from message metadata",
          intent: intentAnalysis.intent,
          confidence: intentAnalysis.confidence,
        });
      } else {
        // Perform fresh intent analysis
        intentAnalysis = await this.analyzeIntent(
          message.content,
          chat.location_id
        );

        // Store intent analysis in message metadata for future reference
        if (!message.metadata) {
          message.metadata = {};
        }

        message.metadata.intent = intentAnalysis.intent;
        message.metadata.confidence = intentAnalysis.confidence;
        if (intentAnalysis.entities.mentionedAgent) {
          message.metadata.mentionedAgent =
            intentAnalysis.entities.mentionedAgent;
        }

        logger.info({
          message: "Added intent analysis to message metadata",
          intent: intentAnalysis.intent,
          context_types: intentAnalysis.entities.requiredContextTypes,
        });
      }

      // Check if message was directed to this agent via @mention (as in base class)
      const wasMentioned =
        message.metadata?.mentionedAgent === agent.name ||
        (message.content &&
          (message.content.match(new RegExp(`@${agent.name}\\b`, "i")) ||
            message.content.match(new RegExp(`@["']${agent.name}["']`, "i")) ||
            (message.metadata?.selected_by === "mention" &&
              message.agent_id === agent.id)));

      // Get chat history
      const contextSize = this.getContextWindowSize();
      const chatHistory = await this.getChatHistoryForModel(
        chat.chat_id,
        contextSize
      );

      // Only get document context if intent analysis indicates it's needed
      let documentContext = "No relevant document context found.";
      if (
        intentAnalysis.entities.needsDocumentSearch ||
        (intentAnalysis.entities.requiredContextTypes &&
          Array.isArray(intentAnalysis.entities.requiredContextTypes) &&
          intentAnalysis.entities.requiredContextTypes.includes(
            "document_data"
          ))
      ) {
        logger.info(
          "Document search needed based on intent analysis, retrieving context"
        );
        documentContext = await this.getDocumentContext(
          chat.location_id,
          message.content
        );
      } else {
        logger.info("Skipping document search based on intent analysis");
      }

      // Current messages for API call - start with system prompt and chat history
      const currentMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: "system",
          content: await this.buildToolSystemPrompt(
            agentConfig,
            true,
            wasMentioned ? agent.name : undefined,
            chat.location_id
          ),
        },
        ...chatHistory,
      ];

      // Include document context only if it was actually found
      if (
        documentContext &&
        documentContext !== "No relevant document context found."
      ) {
        currentMessages.push({
          role: "user",
          content: documentContext,
        });
      }

      // Add product context from message metadata if available
      if (message.metadata?.productContext) {
        currentMessages.push({
          role: "user",
          content: String(message.metadata.productContext),
        });
      }

      // Add current message if not already in history
      const currentMessageId =
        typeof message.id === "number" ? message.id : parseInt(message.id, 10);
      const lastHistoryMessage = chatHistory[chatHistory.length - 1];
      const isCurrentMessageInHistory =
        lastHistoryMessage &&
        lastHistoryMessage.role === "user" &&
        lastHistoryMessage.content === message.content;

      if (!isCurrentMessageInHistory) {
        currentMessages.push({
          role: "user",
          content: message.content,
        });
      }

      // Define tools
      const tools: OpenAI.Chat.ChatCompletionTool[] = [
        {
          type: "function",
          function: {
            name: "data_aggregator",
            description: `Executes structured queries against the dispensary's database to get aggregated data like sales totals, top products, profit margins, or traffic patterns. Use for calculations, totals, or rankings based on historical sales data. DO NOT use for current product inventory, current pricing, or product details - use product_search for those.`,
            parameters: {
              type: "object",
              properties: {
                metric: {
                  type: "string",
                  description: "The specific metric to calculate.",
                  enum: [
                    "total_sales",
                    "total_profit",
                    "transaction_count",
                    "average_transaction_value",
                    "top_products_by_revenue",
                    "top_products_by_quantity",
                    "top_products_by_profit",
                    "profit_margin",
                    "traffic_by_day",
                  ],
                },
                time_period: {
                  type: "string",
                  description:
                    "Time frame for the query (e.g., 'last_month', 'this_month', 'last_30_days', 'last_year', 'this_year', 'ytd', '2024-01'). Defaults to 'last_30_days'.",
                },
                filters: {
                  type: "object",
                  description: "Optional filters to apply.",
                  properties: {
                    category: {
                      type: "string",
                      description: "Filter by product category.",
                    },
                    product_name: {
                      type: "string",
                      description: "Filter by specific product name.",
                    },
                    customer_type: {
                      type: "string",
                      description: "Filter by customer type.",
                    },
                  },
                },
                group_by: {
                  type: "string",
                  description: "Dimension to group results by.",
                  enum: ["category", "product_name", "day_of_week", "month"],
                },
                limit: {
                  type: "number",
                  description: "Maximum number of results to return.",
                  default: 10,
                },
              },
              required: ["metric"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "product_search",
            description: `Performs semantic search over product data to find current product information, including pricing, attributes, and details. ALWAYS use this for queries about products like "most expensive product", "products with highest THC", or searching for specific product attributes. Use for SMOKEY, CRAIG, EZAL agent capabilities.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for product information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "review_search",
            description: `Performs semantic search over review data to find customer reviews and feedback about products or the dispensary. Used by SMOKEY, EZAL, and MRS. PARKER agents.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for review information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "competitor_search",
            description: `Performs semantic search over competitor data to find information about competing dispensaries, their products, and pricing. Used by EZAL agent for market intelligence. IMPORTANT: Always use the user's exact query when searching, do not simplify or extract keywords.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description:
                    "The exact search query for competitor information. Use the full user question without modifying it.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "retailer_search",
            description: `Performs semantic search over retailer data to find information about the dispensary, including locations, hours, services, and policies. Used by POPS, EZAL, and DEEBO agents.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for retailer information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "pos_data_search",
            description: `Performs semantic search over point-of-sale data to find transaction information. Use for queries about past sales transactions. Used by POPS, MONEY MIKE, and CRAIG agents.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description:
                    "The search query for POS transaction information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "insights_search",
            description: `Performs search over business insights and recommendations for the dispensary. Used for queries about business improvement or optimization opportunities.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for business insights.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "customer_search",
            description: `Performs search over customer data to find customer profiles and preferences. Used by MRS. PARKER agent for customer relations.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for customer information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "campaign_search",
            description: `Performs search over marketing campaigns to find campaign information, performance metrics, and targeting details. Used by CRAIG agent for marketing automation.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for campaign information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "list_segment_search",
            description: `Performs search over customer lists and segments to find audience targeting information. Used by CRAIG and MRS. PARKER agents for customer segmentation and targeting.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for list/segment information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "automation_search",
            description: `Performs search over marketing automations and journeys to find information about automated workflows. Used by CRAIG agent for marketing automation.`,
            parameters: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query for automation information.",
                },
                limit: {
                  type: "number",
                  description: "Maximum number of search results.",
                  default: 5,
                },
              },
              required: ["query"],
            },
          },
        },
        {
          type: "function",
          function: {
            name: "product_stats",
            description: `Gets basic statistics about products in inventory, including counts, category breakdowns, and price ranges. Use this for questions like "how many products do we have", "count of products by category", etc. No semantic search is performed.`,
            parameters: {
              type: "object",
              properties: {
                filter_by: {
                  type: "object",
                  description:
                    "Optional filters to apply to the product statistics",
                  properties: {
                    category: {
                      type: "string",
                      description: "Filter by product category",
                    },
                    brand_name: {
                      type: "string",
                      description: "Filter by brand name",
                    },
                    price_range: {
                      type: "object",
                      description: "Filter by price range",
                      properties: {
                        min: {
                          type: "number",
                          description: "Minimum price",
                        },
                        max: {
                          type: "number",
                          description: "Maximum price",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        {
          type: "function",
          function: {
            name: "product_by_name",
            description: `Looks up a specific product by name. Use for follow-up questions about a specific product that was already identified, especially for retrieving pricing or detailed information about a named product.`,
            parameters: {
              type: "object",
              properties: {
                product_name: {
                  type: "string",
                  description:
                    "The exact or partial name of the product to look up",
                },
                exact_match: {
                  type: "boolean",
                  description:
                    "Whether to require an exact match on the name (default: false)",
                  default: false,
                },
              },
              required: ["product_name"],
            },
          },
        },
      ];

      // Initial LLM call (potentially with tool choice)
      let response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: currentMessages,
        tools,
        tool_choice: "auto",
        temperature: this.defaultTemperature,
        max_tokens: this.defaultMaxTokens,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      let responseMessage = response.choices[0].message;

      // Handle Tool Calls (Loop)
      while (responseMessage.tool_calls && iteration < MAX_TOOL_ITERATIONS) {
        iteration++;
        logger.info({
          message: `Tool call requested (Iteration ${iteration})`,
          tool_calls: responseMessage.tool_calls,
          chat_id: chat.chat_id,
        });

        // Add the assistant's tool request message to the history
        currentMessages.push(responseMessage);

        // Process each tool call
        for (const toolCall of responseMessage.tool_calls) {
          let result: any;
          const functionName = toolCall.function.name;
          const functionArgs = JSON.parse(toolCall.function.arguments);

          try {
            if (functionName === "data_aggregator") {
              // Add implicit location_id
              const params: DataAggregationParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeDataAggregationQuery(params);
            } else if (functionName === "vector_search") {
              // Add implicit location_id
              const params: VectorSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeVectorSearch(params);
            } else if (functionName === "product_search") {
              // Add implicit location_id
              const params: ProductSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeProductSearch(params);
            } else if (functionName === "review_search") {
              const params: ReviewSearchParams = {
                ...functionArgs,
              };
              result = await this.executeReviewSearch(params);
            } else if (functionName === "competitor_search") {
              // Add implicit location_id
              const params: CompetitorSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeCompetitorSearch(params);
            } else if (functionName === "retailer_search") {
              const params: RetailerSearchParams = {
                ...functionArgs,
              };
              result = await this.executeRetailerSearch(params);
            } else if (functionName === "pos_data_search") {
              // Add implicit location_id
              const params: PosDataSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executePosDataSearch(params);
            } else if (functionName === "insights_search") {
              // Add implicit location_id
              const params: InsightsSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeInsightsSearch(params);
            } else if (functionName === "customer_search") {
              // Add implicit location_id
              const params: CustomerSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeCustomerSearch(params);
            } else if (functionName === "campaign_search") {
              // Add implicit location_id
              const params: CampaignSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeCampaignSearch(params);
            } else if (functionName === "list_segment_search") {
              // Add implicit location_id
              const params: ListSegmentSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeListSegmentSearch(params);
            } else if (functionName === "automation_search") {
              // Add implicit location_id
              const params: AutomationSearchParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeAutomationSearch(params);
            } else if (functionName === "product_stats") {
              // Add implicit location_id
              const params: ProductStatsParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeProductStats(params);
            } else if (functionName === "product_by_name") {
              // Add implicit location_id
              const params: ProductByNameParams = {
                ...functionArgs,
                location_id: chat.location_id,
              };
              result = await this.executeProductByName(params);
            } else {
              throw new Error(`Unknown tool called: ${functionName}`);
            }

            // Add tool results to message history with correct type assertion
            currentMessages.push({
              role: "tool",
              tool_call_id: toolCall.id,
              content: JSON.stringify(result),
            } as OpenAI.Chat.ChatCompletionToolMessageParam);
          } catch (toolError) {
            logger.error({
              message: "Error executing tool",
              tool: functionName,
              args: functionArgs,
              error: toolError,
              chat_id: chat.chat_id,
            });

            // Fix error type issue
            const errorMessage =
              toolError instanceof Error
                ? toolError.message
                : "Unknown error occurred";

            currentMessages.push({
              role: "tool",
              tool_call_id: toolCall.id,
              content: JSON.stringify({
                error: `Failed to execute tool ${functionName}: ${errorMessage}`,
              }),
            } as OpenAI.Chat.ChatCompletionToolMessageParam);
          }
        }

        // Make the next LLM call with tool results included - fix temperature & max_tokens
        response = await this.openai.chat.completions.create({
          model: this.defaultModel,
          messages: currentMessages,
          tools,
          tool_choice: "auto",
          temperature: this.defaultTemperature,
          max_tokens: this.defaultMaxTokens,
          user: this.currentUserId ? String(this.currentUserId) : undefined,
        });

        responseMessage = response.choices[0].message;
      }

      // Return final response
      if (responseMessage.content) {
        logger.info({
          message: "Final response generated",
          chat_id: chat.chat_id,
          iterations: iteration,
        });

        // Only validate if we're not using a custom prompt
        let isValid = true;
        if (!this.usingCustomPrompt) {
          isValid = await this.validateResponse(responseMessage.content);
          if (!isValid && iteration < MAX_TOOL_ITERATIONS) {
            logger.warn(
              { chat_id: chat.chat_id, agent_id: agent.id },
              "Generated response failed validation"
            );
            return "I apologize, but I couldn't provide an appropriate response. Is there something else I can help with?";
          }
        } else {
          // Log that we're skipping validation for custom prompt
          logger.info({
            message: "Skipping validation for custom prompt response",
            chat_id: chat.chat_id,
            content_preview: responseMessage.content.substring(0, 50),
          });
        }

        return responseMessage.content;
      } else if (responseMessage.tool_calls) {
        logger.warn({
          message: "Exceeded max tool iterations",
          chat_id: chat.chat_id,
        });
        return "I seem to be having trouble completing that request. Could you try rephrasing your question?";
      } else {
        logger.error({
          message: "LLM response was empty",
          chat_id: chat.chat_id,
        });
        return "I apologize, but I couldn't generate a response at this time.";
      }
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";
      let userMessage =
        "I encountered an unexpected error. Please try again or contact support if the issue persists.";

      if (
        error?.status === 429 ||
        error?.code === "insufficient_quota" ||
        error?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        userMessage =
          "I'm currently experiencing high demand. Please try again in a few moments, or contact support if this continues.";

        logger.warn({
          message: "OpenAI quota exceeded during chat response generation",
          chat_id: chat.chat_id,
          agent_id: agent.id,
          quotaExceeded: true,
        });
      } else if (error?.status === 401) {
        errorType = "authentication_failed";
        userMessage =
          "There's a configuration issue with the AI service. Please contact support.";
      } else if (error?.status === 403) {
        errorType = "forbidden";
        userMessage =
          "I don't have permission to process this request. Please contact support.";
      } else if (error?.status >= 500) {
        errorType = "server_error";
        userMessage =
          "The AI service is temporarily unavailable. Please try again in a few moments.";
      } else {
        logger.error(
          {
            error,
            chat_id: chat.chat_id,
            agent_id: agent.id,
            error_message: error.message,
            errorType,
            status: error?.status,
            code: error?.code,
          },
          "Error in ChatAIServiceWithTools.generateResponse"
        );
      }

      return userMessage;
    }
  }

  /**
   * Determine if a query is likely a product recommendation request
   */
  private isProductRecommendationQuery(content: string): boolean {
    const recommendationPatterns = [
      /recommend/i,
      /suggest/i,
      /what (product|strain)/i,
      /looking for/i,
      /best (product|strain)/i,
      /good for/i,
      /help me find/i,
      /top (product|strain)/i,
      /what.*(good|best)/i,
      /similar to/i,
    ];

    return recommendationPatterns.some((pattern) => pattern.test(content));
  }

  /**
   * Generate product recommendations specifically for Smokey agent
   */
  private async generateProductRecommendation(
    chat: Chat,
    message: Message,
    agent: Agent
  ): Promise<string> {
    try {
      // 1. Get relevant products from vector search
      const productQuery = message.content.trim();
      const products = await ProductDataVectorService.queryProductData(
        productQuery,
        { location_id: chat.location_id },
        8 // Get up to 8 products
      );

      if (!products || products.length === 0) {
        // Fall back to standard response if no products found
        logger.info({
          message:
            "No products found for recommendation, falling back to standard response",
          query: productQuery,
          location_id: chat.location_id,
        });
        return this.generateStandardResponse(chat, message, agent);
      }

      // 2. Get top selling products for additional context
      let topProducts: any[] = [];
      try {
        const topProductsResult = await this.executeDataAggregationQuery({
          metric: "top_products_by_quantity",
          location_id: chat.location_id,
          time_period: "last_30_days",
          limit: 5,
        });
        topProducts = topProductsResult || [];
      } catch (error) {
        logger.warn("Error getting top products:", error);
      }

      // 3. Generate a response with product recommendations
      // Get system prompt specific to product recommendations
      const systemPrompt = await this.buildProductRecommendationPrompt(agent);

      // Type cast the products for proper typing
      const typedProducts = products as unknown as ProductVectorResult[];

      // Format products for context
      const productsContext = typedProducts
        .map(
          (p) =>
            `- ${p.product_name || "Unknown product"} (${
              p.category || "Unknown category"
            }): ${p.description || "No description available"}. THC: ${
              p.thc_percentage || "Unknown"
            }%, CBD: ${p.cbd_percentage || "Unknown"}%. Price: $${
              p.price || p.latest_price || "N/A"
            }.`
        )
        .join("\n");

      // Format top products for context
      const topProductsContext =
        topProducts.length > 0
          ? "Top selling products:\n" +
            topProducts
              .map(
                (p) =>
                  `- ${p.product_name}: Sold ${p.total_quantity} units in the last 30 days`
              )
              .join("\n")
          : "No top selling product data available.";

      // Create a conversation with all the context
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        { role: "system", content: systemPrompt },
        {
          role: "user",
          content: `Customer query: ${message.content}\n\nAvailable products that match this query:\n${productsContext}\n\n${topProductsContext}`,
        },
      ];

      // Generate response
      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages,
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const responseText = completion.choices[0].message.content || "{}";

      try {
        // Parse the JSON response
        const responseData = JSON.parse(responseText);

        // Add the product data to message metadata
        if (!message.metadata) {
          message.metadata = {};
        }

        // Attach product data to the message
        message.metadata.data = {
          products: this.processProductsForFrontend(products),
          suggested_questions: responseData.suggested_questions || [],
        };

        // Return the text response
        return (
          responseData.response ||
          "I'm sorry, I couldn't generate product recommendations at this time."
        );
      } catch (error) {
        logger.error("Error parsing product recommendation JSON:", error);
        return responseText; // Return the raw response if parsing fails
      }
    } catch (error) {
      logger.error("Error generating product recommendations:", error);
      return this.generateStandardResponse(chat, message, agent);
    }
  }

  /**
   * Process product objects for frontend display
   */
  private processProductsForFrontend(products: any[]): any[] {
    return products.map((product) => {
      // If image_url is not set, use a placeholder
      if (!product.image_url) {
        product.image_url =
          "https://placehold.co/300x300/65715F/FFFFFF?text=Product";
      }

      // Format price
      if (product.price) {
        product.price = parseFloat(product.price);
      } else if (product.latest_price) {
        product.price = parseFloat(product.latest_price);
      }

      // Ensure product_id exists
      if (!product.product_id && product.id) {
        product.product_id = product.id;
      }

      return {
        product_id: product.product_id || product.id,
        id: product.id,
        product_name: product.product_name,
        description: product.description || "No description available",
        category: product.category || "Uncategorized",
        image_url: product.image_url,
        price: product.price || product.latest_price,
        latest_price: product.latest_price,
        original_price: product.original_price,
        brand_name: product.brand_name,
        display_weight: product.display_weight || product.weight || "",
        thc_percentage: product.thc_percentage || product.thc || "N/A",
        cbd_percentage: product.cbd_percentage || product.cbd || "N/A",
      };
    });
  }

  /**
   * Build a specialized prompt for product recommendations
   */
  private async buildProductRecommendationPrompt(
    agent: Agent
  ): Promise<string> {
    return `You are ${agent.name}, a cannabis product recommendation specialist. Your role is to help customers find the right cannabis products based on their needs, preferences, and questions.

GUIDELINES:
1. Provide friendly, informative recommendations based on the available product data.
2. Explain your recommendations, considering factors like effects, THC/CBD content, and customer preferences.
3. Do not invent or make up product information not present in the data.
4. Only recommend products that are in the provided product list.
5. Format your response as a valid JSON object with the following keys:
   - "response": Your helpful text response to the customer, formatted with proper markdown.
   - "suggested_questions": An array of 2-3 follow-up questions the customer might want to ask.

Your output MUST be a valid JSON object that can be parsed with JSON.parse().

Example JSON structure:
{
  "response": "Based on your preference for relaxation, I'd recommend trying Blue Dream which has balanced effects with 18% THC and 0.5% CBD. It's known for providing relaxation without heavy sedation.",
  "suggested_questions": [
    "What strains are good for sleep?",
    "Do you have products with high CBD content?",
    "Can you recommend a vape pen for beginners?"
  ]
}`;
  }

  /**
   * Generate a standard text response without special formatting
   */
  private async generateStandardResponse(
    chat: Chat,
    message: Message,
    agent: Agent
  ): Promise<string> {
    const agentConfig = this.agentsConfig.agents[agent.id.toString()];
    if (!agentConfig) {
      throw new Error(`Agent configuration not found for ID: ${agent.id}`);
    }

    const contextSize = this.getContextWindowSize();
    const chatHistory = await this.getChatHistoryForModel(
      chat.chat_id,
      contextSize
    );

    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: await this.buildToolSystemPrompt(
          agentConfig,
          false,
          undefined,
          chat.location_id
        ),
      },
      ...chatHistory,
      {
        role: "user",
        content: message.content,
      },
    ];

    const completion = await this.openai.chat.completions.create({
      model: this.defaultModel,
      messages,
      temperature: this.defaultTemperature,
      max_tokens: this.defaultMaxTokens,
      user: this.currentUserId ? String(this.currentUserId) : undefined,
    });

    return (
      completion.choices[0].message.content ||
      "I'm sorry, I couldn't process your request at this time."
    );
  }

  /**
   * Execute a data aggregation query using secure, parameterized SQL
   */
  private async executeDataAggregationQuery(
    params: DataAggregationParams
  ): Promise<any> {
    logger.info({ message: "Executing data aggregation tool", params });

    const {
      metric,
      location_id,
      time_period,
      filters,
      group_by,
      limit = 10,
    } = params;
    let query = this.db<any, any[]>("pos_data").where(
      "location_id",
      location_id
    );

    // Apply Time Period Filter
    const timeCondition = this.getTimePeriodCondition(time_period);
    if (timeCondition.sql) {
      query = query.whereRaw(timeCondition.sql, timeCondition.bindings);
    }

    // Apply Other Filters
    if (filters?.category) {
      query = query.where("master_category", filters.category);
    }
    if (filters?.product_name) {
      query = query.where("product_name", filters.product_name);
    }
    if (filters?.customer_type) {
      query = query.where("customer_type", filters.customer_type);
    }

    // Apply Group By
    if (group_by === "category") {
      query = query.groupBy("master_category");
    } else if (group_by === "product_name") {
      query = query.groupBy("product_name");
    } else if (group_by === "day_of_week") {
      query = query
        .select(this.db.raw("DAYNAME(order_date) as day_name"))
        .groupByRaw("DAYOFWEEK(order_date), DAYNAME(order_date)");
    } else if (group_by === "month") {
      query = query
        .select(this.db.raw('DATE_FORMAT(order_date, "%Y-%m") as month'))
        .groupByRaw('DATE_FORMAT(order_date, "%Y-%m")');
    }

    try {
      // Create variables outside switch for lexical declarations
      let avgResult;
      let marginQuery;

      switch (metric) {
        case "total_sales":
          return await query.sum("gross_sales as total").first();
        case "total_profit":
          return await query.sum("inventory_profit as total").first();
        case "transaction_count":
          return await query.count("* as total").first();
        case "average_transaction_value":
          avgResult = await query
            .select(this.db.raw("SUM(gross_sales) / COUNT(*) as average"))
            .first();
          return avgResult;
        case "top_products_by_revenue":
          return await query
            .select("product_name")
            .sum("gross_sales as total_revenue")
            .groupBy("product_name")
            .orderBy("total_revenue", "desc")
            .limit(limit);
        case "top_products_by_quantity":
          return await query
            .select("product_name")
            .sum("quantity as total_quantity")
            .groupBy("product_name")
            .orderBy("total_quantity", "desc")
            .limit(limit);
        case "top_products_by_profit":
          return await query
            .select("product_name")
            .sum("inventory_profit as total_profit")
            .groupBy("product_name")
            .orderBy("total_profit", "desc")
            .limit(limit);
        case "profit_margin":
          marginQuery = query
            .select(
              this.db.raw("SUM(inventory_profit) as total_profit"),
              this.db.raw("SUM(gross_sales) as total_sales"),
              this.db.raw(
                "(SUM(inventory_profit) / SUM(gross_sales)) * 100 as margin_percentage"
              )
            )
            .where("gross_sales", ">", 0);

          if (group_by === "product_name") {
            return await marginQuery
              .select("product_name")
              .orderBy("margin_percentage", "desc")
              .limit(limit);
          } else if (group_by === "category") {
            return await marginQuery
              .select("master_category")
              .orderBy("margin_percentage", "desc")
              .limit(limit);
          } else {
            return await marginQuery.first();
          }
        case "traffic_by_day":
          if (group_by !== "day_of_week") {
            throw new Error(
              "group_by must be 'day_of_week' for traffic_by_day metric"
            );
          }
          return await query
            .count("* as transaction_count")
            .orderBy("transaction_count", "desc");
        default:
          throw new Error(`Unsupported metric: ${metric}`);
      }
    } catch (error: any) {
      logger.error({
        message: "Error executing aggregation query",
        metric,
        error,
      });
      throw new Error(
        `Database error while fetching ${metric}: ${error.message}`
      );
    }
  }

  /**
   * Execute vector searches using the appropriate services
   */
  private async executeVectorSearch(params: VectorSearchParams): Promise<any> {
    logger.info({ message: "Executing vector search tool", params });
    const { query, location_id, data_types, limit = 5 } = params;
    const combinedResults: any[] = [];

    // Use Promise.allSettled to run searches concurrently
    const searchPromises = data_types.map(async (type) => {
      try {
        switch (type) {
          case "pos_data":
            return await PosDataVectorService.queryPosData(
              query,
              location_id,
              limit
            );
          case "product_data":
            return await ProductDataVectorService.queryProductData(
              query,
              { location_id },
              limit
            );
          case "review_data":
            return await ReviewDataVectorService.queryReviewData(
              query,
              { source_type: "review" },
              limit
            );
          case "customer_data":
            // Placeholder - implement when service is available
            logger.warn("Customer data vector search not fully implemented");
            return [];
          case "retailer_data":
            return await RetailerDataVectorService.queryRetailerData(
              query,
              {},
              limit
            );
          case "competitor_data":
            return await CompetitorDataVectorService.queryCompetitorData(
              query,
              location_id,
              limit
            );
          default:
            logger.warn(`Unsupported vector search data type: ${type}`);
            return [];
        }
      } catch (error: any) {
        logger.error({
          message: `Error searching vector type ${type}`,
          query,
          error,
        });
        return { error: `Failed to search ${type}`, details: error.message };
      }
    });

    const results = await Promise.allSettled(searchPromises);

    // Process results, combining successful searches
    results.forEach((result, index) => {
      const type = data_types[index];
      if (result.status === "fulfilled") {
        if (
          result.value &&
          !Array.isArray(result.value) &&
          "error" in result.value
        ) {
          logger.error({
            message: `Vector search failed for type ${type}`,
            error: result.value,
          });
        } else if (Array.isArray(result.value) && result.value.length > 0) {
          combinedResults.push(
            ...result.value.map((item) => ({ ...item, source_type: type }))
          );
        }
      } else {
        logger.error({
          message: `Vector search promise rejected for type ${type}`,
          reason: result.reason,
        });
      }
    });

    return combinedResults.slice(0, limit * 2); // Limit the total number of results
  }

  /**
   * Execute product data search
   */
  private async executeProductSearch(
    params: ProductSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing product search tool", params });
    const { query, location_id, limit = 5 } = params;

    if (!query || query.trim() === "") {
      logger.warn(
        "Empty query provided to product_search, returning error message"
      );
      return [
        {
          source_type: "product_data",
          content:
            "Please provide a search query for product search. For general product statistics, use the product_stats tool instead.",
          score: 0.5,
        },
      ];
    }

    try {
      const results = await ProductDataVectorService.queryProductData(
        query,
        { location_id },
        limit
      );

      // Enhance the results with better formatted price and metadata
      return results.map((item: VectorQueryResult) => {
        const metadata = item.metadata || {};

        // Create a more useful content string that includes price
        const priceDisplay = metadata.latest_price
          ? `$${parseFloat(metadata.latest_price).toFixed(2)}`
          : "Price not available";

        // THC/CBD content
        const thcContent =
          metadata.percentage_thc || metadata.thc
            ? `${metadata.percentage_thc || metadata.thc}%`
            : "Not specified";
        const cbdContent =
          metadata.percentage_cbd || metadata.cbd
            ? `${metadata.percentage_cbd || metadata.cbd}%`
            : "Not specified";

        // Format content for better readability
        const content =
          `Product: ${metadata.product_name || "Unknown"}\n` +
          `Category: ${metadata.category || "Unknown"}\n` +
          `Brand: ${metadata.brand_name || "Unknown"}\n` +
          `Price: ${priceDisplay}\n` +
          `THC: ${thcContent}\n` +
          `CBD: ${cbdContent}\n` +
          `Description: ${metadata.description || "No description available"}`;

        return {
          ...item,
          source_type: "product_data",
          content,
          price: metadata.latest_price
            ? parseFloat(metadata.latest_price)
            : null,
          product_name: metadata.product_name,
        };
      });
    } catch (error: any) {
      logger.error({
        message: "Error searching product data",
        query,
        error,
      });
      return { error: `Failed to search product data: ${error.message}` };
    }
  }

  /**
   * Execute review data search
   */
  private async executeReviewSearch(params: ReviewSearchParams): Promise<any> {
    logger.info({ message: "Executing review search tool", params });
    const { query, limit = 5 } = params;

    try {
      const results = await ReviewDataVectorService.queryReviewData(
        query,
        { source_type: "review" },
        limit
      );

      return results.map((item: VectorQueryResult) => ({
        ...item,
        source_type: "review_data",
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching review data",
        query,
        error,
      });
      return { error: `Failed to search review data: ${error.message}` };
    }
  }

  /**
   * Execute competitor data search
   */
  private async executeCompetitorSearch(
    params: CompetitorSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing competitor search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      // Use entire query for CompetitorAnalysisService first for highest-quality results
      try {
        logger.info({
          message: "Using CompetitorAnalysisService for direct query",
          query,
        });
        const competitorInfo =
          await new CompetitorAnalysisService().getSpecificCompetitorInfo(
            location_id,
            query
          );
        return [
          {
            source_type: "competitor_info",
            content: competitorInfo,
            score: 1.0,
            metadata: {
              query_type: "direct_competitor_info",
            },
          },
        ];
      } catch (error) {
        logger.error({
          message:
            "Error getting competitor info, falling back to vector search",
          query,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
        // Continue to vector search if this fails
      }

      // Default: search for competitor data using vector search
      const results = await CompetitorDataVectorService.queryCompetitorData(
        query,
        location_id,
        limit
      );

      return results.map((item: VectorQueryResult) => ({
        ...item,
        source_type: "competitor_data",
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching competitor data",
        query,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      return { error: `Failed to search competitor data: ${error.message}` };
    }
  }

  /**
   * Execute retailer data search
   */
  private async executeRetailerSearch(
    params: RetailerSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing retailer search tool", params });
    const { query, limit = 5 } = params;

    try {
      const results = await RetailerDataVectorService.queryRetailerData(
        query,
        {},
        limit
      );

      return results.map((item: VectorQueryResult) => ({
        ...item,
        source_type: "retailer_data",
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching retailer data",
        query,
        error,
      });
      return { error: `Failed to search retailer data: ${error.message}` };
    }
  }

  /**
   * Gets chat history in the format required for OpenAI API calls
   */
  private async getChatHistoryForModel(
    chatId: number | string,
    maxMessagesToFetch: number
  ): Promise<OpenAI.Chat.ChatCompletionMessageParam[]> {
    // Convert chatId if needed
    const dbChatId = typeof chatId === "number" ? chatId.toString() : chatId;

    // Use getModelInfo() or similar accessor method if needed
    const contextWindowSize = this.getContextWindowSize();

    // Fetch recent messages from the database - use the passed-in parameter
    const messages = await this.db("messages")
      .where("chat_id", dbChatId)
      .orderBy("timestamp", "desc")
      .limit(maxMessagesToFetch);

    // Reverse to get chronological order and map to OpenAI format
    return messages.reverse().map((msg) => ({
      role: msg.role === "user" ? "user" : "assistant",
      content: msg.content,
    }));
  }

  /**
   * Build an enhanced system prompt that includes information about available tools
   */
  private async buildToolSystemPrompt(
    agentConfig: AgentConfig,
    toolsAvailable: boolean = false,
    mentionedAgentName?: string,
    locationId?: number
  ): Promise<string> {
    // Reset the custom prompt flag
    this.usingCustomPrompt = false;

    // Get competitor names if locationId is provided
    let competitorNames: string[] = [];
    if (locationId) {
      try {
        competitorNames = await this.getCompetitorNamesForLocation(locationId);
      } catch (error) {
        logger.error("Error getting competitor names for prompt:", error);
      }
    }

    // Base prompt that's always included
    const basePrompt = `${
      agentConfig.systemPrompt ||
      `You are ${agentConfig.name}, a helpful assistant.`
    }

Key Capabilities:
${
  agentConfig.capabilities?.map((cap) => `- ${cap}`).join("\n") ||
  "- General conversation"
}`;

    // Add competitor names to the prompt if available
    let competitorContext = "";
    if (competitorNames.length > 0) {
      competitorContext = `\n\nCompetitors Information:
The business has the following competitors in their area:
${competitorNames.map((name) => `- ${name}`).join("\n")}

When answering questions about competitors, reference this list when appropriate.`;
    }

    // Standard guidelines that are always included
    const additionalPrompt = `
Guidelines:
1. Always maintain a professional and friendly tone aligned with your role: ${agentConfig.role}.
2. Provide accurate information based on available data or tools.
3. Respect privacy and compliance requirements for cannabis businesses.
4. If unsure or unable to answer, acknowledge limitations and suggest alternatives.
5. Keep responses concise and relevant to the user's query.
6. When using the data_aggregator tool, clearly state the results, including the time period used.
7. When using the vector_search tool, synthesize the information found, don't just list raw results.
8. Prioritize using tools for specific data requests rather than relying solely on general knowledge.
9. When displaying tables (like events, products, or data), use standard HTML tables without inline styles.
10. For product recommendations, include structured data in your response that the app can use to display products.
11. For graphs and charts, structure your data in a way the app can visualize it properly.`;

    // Add table formatting example
    const tableExample = `
For tables, use a structure like this (without any inline styles - the app's CSS will handle styling and dark mode):
<table>
  <thead>
    <tr>
      <th>Event Name</th>
      <th>Date</th>
      <th>Location</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Cannabis Expo</td>
      <td>March 15-17, 2025</td>
      <td>Denver, CO</td>
    </tr>
  </tbody>
</table>`;

    // Add product recommendations example
    const productsExample = `
For product recommendations, structure your response like this JSON format within markdown:
\`\`\`json
{
  "text": "Based on your preferences, here are some product recommendations...",
  "data": {
    "type": "products",
    "products": [
      {
        "id": "product-1",
        "name": "Blue Dream",
        "category": "Flower",
        "price": 35.99,
        "thc_percentage": 18.5,
        "cbd_percentage": 0.5,
        "description": "A sativa-dominant hybrid with blueberry and haze notes",
        "image_url": "https://example.com/images/blue-dream.jpg", 
        "strain_type": "Sativa-dominant Hybrid"
      }
    ],
    "suggested_next_questions": [
      "What are your best indica strains?",
      "Do you have any CBD products?"
    ]
  }
}
\`\`\`
Only use this format when specifically recommending cannabis products to users.`;

    // Add graph data example
    const graphExample = `
For structured data that needs to be visualized, use this format:
\`\`\`json
{
  "text": "Here's a breakdown of sales by category...",
  "data": {
    "type": "graph",
    "graph": {
      "title": "Sales by Category",
      "graphData": {
        "type": "table",
        "headers": ["Category", "Revenue", "Units Sold", "Profit Margin"],
        "rows": [
          ["Flower", "$25,430", "450", "32%"],
          ["Edibles", "$12,750", "325", "28%"],
          ["Concentrates", "$18,200", "215", "35%"]
        ]
      }
    }
  }
}
\`\`\`
This allows the app to visualize the data in the most appropriate way.`;

    // Add tool availability if applicable
    let toolsPrompt = "";
    if (toolsAvailable) {
      toolsPrompt = `\n\nYou have access to specialized tools:
- 'data_aggregator': Use this for calculations, totals, rankings (e.g., "total sales last month", "top 5 products by profit").
- 'vector_search': Use this to find specific information about products, reviews, customers, competitors etc. (e.g., "details about Blue Dream", "latest reviews for store X").`;
    }

    // Add mention acknowledgment if applicable
    let mentionPrompt = "";
    if (mentionedAgentName) {
      mentionPrompt = `\n\nImportant: The user has specifically mentioned you by name (@${mentionedAgentName}). Acknowledge this direct mention at the beginning of your response.`;
    }

    // Add guidance for using different tools based on query type
    const queryGuidance = `\n\nWhen handling different types of queries:
- For product information (prices, details, attributes): use 'product_search'
- For questions like "most expensive product" or "highest THC products": use 'product_search'
- For follow-up questions about a specific product already mentioned: use 'product_by_name'
- For questions about product counts, totals, or statistics: use 'product_stats'
- For historical sales analysis (what HAS SOLD): use 'data_aggregator'
- For customer reviews and feedback: use 'review_search'
- For competitor analysis and insights: use 'competitor_search'
- For dispensary information (hours, policies): use 'retailer_search'
- For transaction data and sales history: use 'pos_data_search'
- For business insights and recommendations: use 'insights_search'
- For customer profiles and preferences: use 'customer_search'
- For marketing campaign information: use 'campaign_search'
- For customer lists and segments: use 'list_segment_search'
- For marketing automation workflows: use 'automation_search'`;

    // Check if user has an active custom prompt
    let customPromptContent = "";
    try {
      if (this.db && this.currentUserId) {
        const customPrompt = await this.db
          .from("user_custom_prompts")
          .where({
            admin_id: this.currentUserId,
            is_active: true,
          })
          .orderBy("updated_at", "desc")
          .first();

        if (customPrompt) {
          // Mark that we're using a custom prompt, but not completely overriding
          this.usingCustomPrompt = true;

          logger.info({
            message: "Adding custom prompt to system prompt",
            admin_id: this.currentUserId,
            prompt_id: customPrompt.id,
            prompt_name: customPrompt.name,
          });

          // Add custom prompt content with a precedence notice
          customPromptContent = `\n\n========== CUSTOM INSTRUCTIONS ==========\n
${customPrompt.content}
\n===========================================\n
Note: When there is a conflict between these custom instructions and the standard guidelines, 
the custom instructions take precedence. However, continue to use all tools and capabilities as instructed above.`;
        }
      }
    } catch (error) {
      logger.error({
        message: "Error fetching custom prompt",
        error,
        admin_id: this.currentUserId,
      });
    }

    // Combine all prompt sections
    const finalPrompt =
      basePrompt +
      competitorContext +
      additionalPrompt +
      tableExample +
      productsExample +
      graphExample +
      toolsPrompt +
      mentionPrompt +
      queryGuidance +
      customPromptContent;

    return finalPrompt;
  }

  // Add a setter for the current user ID from the auth context
  setCurrentUserId(userId: number): void {
    this.currentUserId = userId;
  }

  /**
   * Generate SQL WHERE clause conditions based on time period string
   */
  private getTimePeriodCondition(time_period?: string): {
    sql: string | null;
    bindings: any[];
  } {
    if (!time_period) {
      // Default to last 30 days if not specified
      return {
        sql: "order_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)",
        bindings: [],
      };
    }

    const lowerTimePeriod = time_period.toLowerCase();

    // Specific keywords
    if (lowerTimePeriod === "last_month") {
      return {
        sql: "YEAR(order_date) = YEAR(CURRENT_DATE - INTERVAL 1 MONTH) AND MONTH(order_date) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH)",
        bindings: [],
      };
    }
    if (lowerTimePeriod === "this_month") {
      return {
        sql: "YEAR(order_date) = YEAR(CURDATE()) AND MONTH(order_date) = MONTH(CURDATE())",
        bindings: [],
      };
    }
    if (lowerTimePeriod === "last_30_days") {
      return {
        sql: "order_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)",
        bindings: [],
      };
    }
    if (lowerTimePeriod === "last_year") {
      return {
        sql: "YEAR(order_date) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 YEAR))",
        bindings: [],
      };
    }
    if (lowerTimePeriod === "this_year") {
      return {
        sql: "YEAR(order_date) = YEAR(CURDATE())",
        bindings: [],
      };
    }
    if (lowerTimePeriod === "ytd") {
      return {
        sql: "YEAR(order_date) = YEAR(CURDATE()) AND order_date <= CURDATE()",
        bindings: [],
      };
    }

    // Check for YYYY-MM format (e.g., "2024-01")
    const yearMonthMatch = time_period.match(/^(\d{4})-(\d{2})$/);
    if (yearMonthMatch) {
      const year = parseInt(yearMonthMatch[1], 10);
      const month = parseInt(yearMonthMatch[2], 10);
      if (year > 2000 && month >= 1 && month <= 12) {
        return {
          sql: "YEAR(order_date) = ? AND MONTH(order_date) = ?",
          bindings: [year, month],
        };
      }
    }

    // Check for Month Name [Year] format (e.g., "January 2024", "Feb")
    const monthNames = [
      "jan",
      "feb",
      "mar",
      "apr",
      "may",
      "jun",
      "jul",
      "aug",
      "sep",
      "oct",
      "nov",
      "dec",
    ];
    let monthNum = -1;

    for (let i = 0; i < monthNames.length; i++) {
      if (lowerTimePeriod.includes(monthNames[i])) {
        monthNum = i + 1;
        break;
      }
    }

    if (monthNum !== -1) {
      const yearMatch = lowerTimePeriod.match(/\b(20\d{2})\b/);
      const year = yearMatch
        ? parseInt(yearMatch[1], 10)
        : new Date().getFullYear();
      return {
        sql: "YEAR(order_date) = ? AND MONTH(order_date) = ?",
        bindings: [year, monthNum],
      };
    }

    logger.warn(
      `Unsupported time_period format: ${time_period}. Defaulting to last 30 days.`
    );
    return {
      sql: "order_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)",
      bindings: [],
    };
  }

  /**
   * Access the protected openai property
   */
  public getOpenAI(): OpenAI {
    return this.openai;
  }

  // Add accessor method for parent class's contextWindow
  private getContextWindowSize(): number {
    // Use getModelInfo() to avoid direct access to protected contextWindow
    return 15; // Default fallback value
  }

  // Add accessor methods to safely interact with parent class private properties
  private getTemperature(): number {
    // We know the default is 0.7 from the parent class
    return 0.7;
  }

  private getMaxTokens(): number {
    return 32768;
  }

  /**
   * Retrieves document context from vectors relevant to the given query
   * @param location_id The location ID
   * @param query The user's question or message
   * @returns The relevant document context formatted for inclusion in prompts
   */
  async getDocumentContext(
    location_id: number,
    query: string
  ): Promise<string> {
    try {
      logger.info(
        `Retrieving document context for location ${location_id}, query: "${query.substring(
          0,
          50
        )}..."`
      );

      // Import dynamically to avoid circular dependencies
      const { DocumentVectorService } = await import(
        "../documents/DocumentVectorService"
      );

      // Search document chunks using our custom vector service
      const results = await DocumentVectorService.searchDocumentChunks(
        query,
        location_id,
        5
      );

      if (!results || results.length === 0) {
        logger.info(
          `No document snippets found for query: "${query.substring(0, 50)}..."`
        );
        return "No relevant document context found.";
      }

      // Format the results into a cohesive context
      const contextParts = results.map((result, index) => {
        const documentName =
          result.metadata.document_name || "Unknown document";
        const documentType = result.metadata.document_type || "Unknown type";
        const chunkIndex = result.metadata.chunk_index || 0;
        const totalChunks = result.metadata.total_chunks || 1;

        // Build a source reference string
        const source = `${documentName} (${documentType})`;
        const position = `Part ${chunkIndex + 1}/${totalChunks}`;
        const sourceInfo = `${source} ${position}`;

        return `[Document ${index + 1}: ${sourceInfo}]\n${result.content}\n`;
      });

      return `Document context:\n${contextParts.join("\n")}`;
    } catch (error) {
      // Don't fail the entire request if document context fails
      logger.error(
        `Error retrieving document context: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        error
      );
      return "Error retrieving document context. Proceeding without document information.";
    }
  }

  /**
   * Execute POS data search
   */
  private async executePosDataSearch(
    params: PosDataSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing POS data search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      const results = await PosDataVectorService.queryPosData(
        query,
        location_id,
        limit
      );

      return results.map((item) => ({
        ...item,
        source_type: "pos_data",
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching POS data",
        query,
        error,
      });
      return { error: `Failed to search POS data: ${error.message}` };
    }
  }

  /**
   * Execute insights search
   */
  private async executeInsightsSearch(
    params: InsightsSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing insights search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      // Note: This is a placeholder. We need to implement a vector service for insights
      // For now, we'll return a message indicating this functionality is coming soon
      return [
        {
          source_type: "insights",
          content:
            "Insights search functionality is coming soon. Currently, please use data_aggregator to analyze business metrics.",
          score: 1.0,
        },
      ];
    } catch (error: any) {
      logger.error({
        message: "Error searching insights data",
        query,
        error,
      });
      return { error: `Failed to search insights data: ${error.message}` };
    }
  }

  /**
   * Execute customer search
   */
  private async executeCustomerSearch(
    params: CustomerSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing customer search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      // Note: This is a placeholder. We need to implement a vector service for customer data
      // For now, we'll return a message indicating this functionality is coming soon
      return [
        {
          source_type: "customer_data",
          content:
            "Customer search functionality is coming soon. Please check back later.",
          score: 1.0,
        },
      ];
    } catch (error: any) {
      logger.error({
        message: "Error searching customer data",
        query,
        error,
      });
      return { error: `Failed to search customer data: ${error.message}` };
    }
  }

  /**
   * Execute campaign search
   */
  private async executeCampaignSearch(
    params: CampaignSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing campaign search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      // Query the database for campaign information
      // This uses direct database queries since we may not have a vector service for campaigns yet
      const campaigns = await this.db("campaigns")
        .where("location_id", location_id)
        .whereRaw("LOWER(name) LIKE ?", [`%${query.toLowerCase()}%`])
        .orWhereRaw("LOWER(subject) LIKE ?", [`%${query.toLowerCase()}%`])
        .limit(limit)
        .select(
          "id",
          "name",
          "subject",
          "body",
          "status",
          "created_at",
          "updated_at",
          "stats"
        );

      if (campaigns.length === 0) {
        return [
          {
            source_type: "campaign_data",
            content: `No campaigns found matching query: "${query}"`,
            score: 0.5,
          },
        ];
      }

      return campaigns.map((campaign) => ({
        source_type: "campaign_data",
        content: `Campaign: ${campaign.name}\nSubject: ${
          campaign.subject || "N/A"
        }\nStatus: ${campaign.status || "N/A"}\nCreated: ${new Date(
          campaign.created_at
        ).toLocaleDateString()}`,
        metadata: campaign,
        score: 1.0,
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching campaign data",
        query,
        error,
      });
      return { error: `Failed to search campaign data: ${error.message}` };
    }
  }

  /**
   * Execute list/segment search
   */
  private async executeListSegmentSearch(
    params: ListSegmentSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing list/segment search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      // Query the database for list information
      const lists = await this.db("lists")
        .where("location_id", location_id)
        .whereRaw("LOWER(name) LIKE ?", [`%${query.toLowerCase()}%`])
        .orWhereRaw("LOWER(description) LIKE ?", [`%${query.toLowerCase()}%`])
        .limit(limit)
        .select(
          "id",
          "name",
          "description",
          "total",
          "created_at",
          "updated_at"
        );

      if (lists.length === 0) {
        return [
          {
            source_type: "list_segment_data",
            content: `No lists or segments found matching query: "${query}"`,
            score: 0.5,
          },
        ];
      }

      return lists.map((list) => ({
        source_type: "list_segment_data",
        content: `List: ${list.name}\nDescription: ${
          list.description || "N/A"
        }\nTotal Subscribers: ${list.total || 0}\nCreated: ${new Date(
          list.created_at
        ).toLocaleDateString()}`,
        metadata: list,
        score: 1.0,
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching list/segment data",
        query,
        error,
      });
      return { error: `Failed to search list/segment data: ${error.message}` };
    }
  }

  /**
   * Execute automation search
   */
  private async executeAutomationSearch(
    params: AutomationSearchParams
  ): Promise<any> {
    logger.info({ message: "Executing automation search tool", params });
    const { query, location_id, limit = 5 } = params;

    try {
      // Query the database for journey (automation) information
      const journeys = await this.db("journeys")
        .where("location_id", location_id)
        .whereRaw("LOWER(name) LIKE ?", [`%${query.toLowerCase()}%`])
        .orWhereRaw("LOWER(description) LIKE ?", [`%${query.toLowerCase()}%`])
        .limit(limit)
        .select(
          "id",
          "name",
          "description",
          "status",
          "stats",
          "created_at",
          "updated_at"
        );

      if (journeys.length === 0) {
        return [
          {
            source_type: "automation_data",
            content: `No automations or journeys found matching query: "${query}"`,
            score: 0.5,
          },
        ];
      }

      return journeys.map((journey) => ({
        source_type: "automation_data",
        content: `Automation: ${journey.name}\nDescription: ${
          journey.description || "N/A"
        }\nStatus: ${journey.status || "N/A"}\nCreated: ${new Date(
          journey.created_at
        ).toLocaleDateString()}`,
        metadata: journey,
        score: 1.0,
      }));
    } catch (error: any) {
      logger.error({
        message: "Error searching automation data",
        query,
        error,
      });
      return { error: `Failed to search automation data: ${error.message}` };
    }
  }

  /**
   * Execute product statistics query
   * This provides counts and basic statistics without requiring vector search
   */
  private async executeProductStats(params: ProductStatsParams): Promise<any> {
    logger.info({ message: "Executing product stats tool", params });
    const { location_id, filter_by } = params;

    try {
      // Start with a base query for the products table
      let query = this.db("products").where("location_id", location_id);

      // Apply filters if provided
      if (filter_by) {
        if (filter_by.category) {
          query = query.where("category", filter_by.category);
        }
        if (filter_by.brand_name) {
          query = query.where("brand_name", filter_by.brand_name);
        }
        if (filter_by.price_range) {
          if (filter_by.price_range.min !== undefined) {
            query = query.where(
              "latest_price",
              ">=",
              filter_by.price_range.min
            );
          }
          if (filter_by.price_range.max !== undefined) {
            query = query.where(
              "latest_price",
              "<=",
              filter_by.price_range.max
            );
          }
        }
      }

      // Get the total count first
      const totalCount = await query.clone().count("* as count").first();

      // Query for category breakdown
      const categoryBreakdown = await query
        .clone()
        .select("category")
        .count("* as count")
        .groupBy("category")
        .orderBy("count", "desc");

      // Query for brand breakdown (top 10)
      const brandBreakdown = await query
        .clone()
        .select("brand_name")
        .whereNotNull("brand_name")
        .count("* as count")
        .groupBy("brand_name")
        .orderBy("count", "desc")
        .limit(10);

      // Query for price statistics
      const priceStats = await query
        .clone()
        .whereNotNull("latest_price")
        .min("latest_price as min_price")
        .max("latest_price as max_price")
        .avg("latest_price as avg_price")
        .first();

      // Query for THC/CBD ranges
      const thcCbdStats = await query
        .clone()
        .whereNotNull("percentage_thc")
        .min("percentage_thc as min_thc")
        .max("percentage_thc as max_thc")
        .avg("percentage_thc as avg_thc")
        .min("percentage_cbd as min_cbd")
        .max("percentage_cbd as max_cbd")
        .avg("percentage_cbd as avg_cbd")
        .first();

      // Prepare result
      return {
        total_products: totalCount ? totalCount.count : 0,
        categories: categoryBreakdown.map((item) => ({
          name: item.category || "Uncategorized",
          count: item.count,
        })),
        top_brands: brandBreakdown.map((item) => ({
          name: item.brand_name || "Unknown",
          count: item.count,
        })),
        price_stats: {
          min: priceStats ? parseFloat(priceStats.min_price) || 0 : 0,
          max: priceStats ? parseFloat(priceStats.max_price) || 0 : 0,
          avg: priceStats ? parseFloat(priceStats.avg_price) || 0 : 0,
        },
        cannabinoid_stats: {
          thc: {
            min: thcCbdStats ? parseFloat(thcCbdStats.min_thc) || 0 : 0,
            max: thcCbdStats ? parseFloat(thcCbdStats.max_thc) || 0 : 0,
            avg: thcCbdStats ? parseFloat(thcCbdStats.avg_thc) || 0 : 0,
          },
          cbd: {
            min: thcCbdStats ? parseFloat(thcCbdStats.min_cbd) || 0 : 0,
            max: thcCbdStats ? parseFloat(thcCbdStats.max_cbd) || 0 : 0,
            avg: thcCbdStats ? parseFloat(thcCbdStats.avg_cbd) || 0 : 0,
          },
        },
        applied_filters: filter_by || {},
      };
    } catch (error: any) {
      logger.error({
        message: "Error getting product statistics",
        error,
        location_id,
      });
      return {
        error: `Failed to get product statistics: ${error.message}`,
        total_products: 0,
      };
    }
  }

  /**
   * Execute product lookup by name
   * This provides a direct lookup mechanism for products without requiring semantic search
   */
  private async executeProductByName(
    params: ProductByNameParams
  ): Promise<any> {
    logger.info({ message: "Executing product lookup by name", params });
    const { product_name, location_id, exact_match = false } = params;

    try {
      // Query the database directly for the product
      let query = this.db("products").where("location_id", location_id);

      if (exact_match) {
        // Case-insensitive exact match
        query = query.whereRaw("LOWER(product_name) = LOWER(?)", [
          product_name,
        ]);
      } else {
        // Case-insensitive partial match
        query = query.whereRaw("LOWER(product_name) LIKE LOWER(?)", [
          `%${product_name}%`,
        ]);
      }

      // Get the products
      const products = await query.select(
        "id",
        "meta_sku",
        "retailer_id",
        "product_name",
        "brand_name",
        "category",
        "subcategory",
        "latest_price",
        "image_url",
        "percentage_thc",
        "percentage_cbd",
        "medical",
        "recreational",
        "display_weight"
      );

      if (products.length === 0) {
        return [
          {
            source_type: "product_data",
            content: `No products found matching "${product_name}". Try using product_search with a more general query.`,
            score: 0.5,
          },
        ];
      }

      // Format the products
      return products.map((product) => {
        const priceDisplay = product.latest_price
          ? `$${parseFloat(product.latest_price).toFixed(2)}`
          : "Price not available";

        const thcContent = product.percentage_thc
          ? `${product.percentage_thc}%`
          : "Not specified";

        const cbdContent = product.percentage_cbd
          ? `${product.percentage_cbd}%`
          : "Not specified";

        const content =
          `Product: ${product.product_name}\n` +
          `Category: ${product.category || "Unknown"}\n` +
          `Brand: ${product.brand_name || "Unknown"}\n` +
          `Price: ${priceDisplay}\n` +
          `THC: ${thcContent}\n` +
          `CBD: ${cbdContent}\n` +
          `Weight: ${product.display_weight || "Not specified"}\n` +
          `Medical: ${product.medical ? "Yes" : "No"}\n` +
          `Recreational: ${product.recreational ? "Yes" : "No"}`;

        return {
          source_type: "product_data",
          content,
          metadata: product,
          price: product.latest_price ? parseFloat(product.latest_price) : null,
          product_name: product.product_name,
          score:
            exact_match &&
            product.product_name.toLowerCase() === product_name.toLowerCase()
              ? 1.0
              : 0.8,
        };
      });
    } catch (error: any) {
      logger.error({
        message: "Error looking up product by name",
        product_name,
        error,
      });
      return { error: `Failed to look up product: ${error.message}` };
    }
  }

  /**
   * Detects @mentions in a message
   * @returns The name of the mentioned agent or null if no mention
   */
  private detectMentions(message: string): string | null {
    // Check for various mention formats

    // 1. Standard @username format
    const standardMentionRegex = /@(\w+)/g;
    const standardMentions = message.match(standardMentionRegex);

    if (standardMentions && standardMentions.length > 0) {
      // Take the first mention (we could handle multiple in the future)
      const firstMention = standardMentions[0];
      // Remove the @ symbol to get just the name
      return firstMention.substring(1);
    }

    // 2. Format with spaces/special chars: @"Agent Name" or @'Agent Name'
    const quotedMentionRegex = /@["']([^"']+)["']/g;
    // Create a new regex instance for the exec call to avoid stateful issues
    const execRegex = new RegExp(
      quotedMentionRegex.source,
      quotedMentionRegex.flags
    );
    const quotedMentions = message.match(quotedMentionRegex);

    if (quotedMentions && quotedMentions.length > 0) {
      // Extract name from quotes
      const match = execRegex.exec(message);
      if (match && match[1]) {
        return match[1]; // Return the captured group (name between quotes)
      }
    }

    // No mentions found
    return null;
  }

  /**
   * Enhanced Intent analysis using LLM that determines both intent and required context types
   */
  async analyzeIntent(
    message: string,
    locationId?: number
  ): Promise<IntentAnalysis> {
    try {
      logger.info({
        message: "Analyzing intent",
        user_message: message,
      });

      // Get a list of competitor names if locationId is provided
      let competitorNames: string[] = [];
      if (locationId) {
        try {
          competitorNames = await this.getCompetitorNamesForLocation(
            locationId
          );
          logger.info({
            message: "Retrieved competitor names for intent analysis",
            competitorNames,
          });
        } catch (error) {
          logger.error(
            "Error getting competitor names for intent analysis:",
            error
          );
        }
      }

      // Competitor names as context for the LLM
      const competitorContext =
        competitorNames.length > 0
          ? `\nThe business has the following competitors in their area:\n${competitorNames
              .map((name) => `- ${name}`)
              .join(
                "\n"
              )}\n\nIf the query mentions any of these specific competitors, consider it competitor-related and include competitor_data in the requiredContextTypes.`
          : "";

      // Check if the message mentions a specific agent
      const mentionedAgent = this.detectMentions(message);

      // Get document context if needed
      let documentContext = "";
      if (locationId) {
        try {
          const docContext = await this.getDocumentContext(locationId, message);
          if (docContext) {
            documentContext = `Document context: ${docContext}`;
          }
        } catch (error) {
          logger.error({
            message: "Error getting document context for intent detection",
            error,
          });
        }
      }

      // @mention takes priority
      if (mentionedAgent) {
        logger.info({
          message: "Agent mention detected",
          mentioned_agent: mentionedAgent,
          user_message: message,
        });

        // Even with mentions, we need the LLM to determine required context types
        try {
          const completion = await this.openai.chat.completions.create({
            model: "gpt-4.1-mini", // Using a smaller, faster model for classification
            messages: [
              {
                role: "system",
                content: `You are an intent classifier for a cannabis dispensary assistant.
A user has directly mentioned the "${mentionedAgent}" agent.
Your job is to determine only which data contexts are required to answer this query accurately.

Possible context types:
- product_data: Information about cannabis products, strains, inventory, pricing, THC/CBD content
- pos_data: Point of sale data, sales transactions, revenue, trends, bestsellers 
- review_data: Customer reviews, ratings, feedback
- competitor_data: Information about competing dispensaries, their products, pricing
- retailer_data: Information about store locations, hours, details
- customer_data: Customer profiles, purchase history, preferences
- document_data: Information from uploaded documents, policies, and guidelines

For queries about bestsellers or popular products, ALWAYS include both product_data AND pos_data.
For ANY queries mentioning sales figures, revenue, financials, or asking about monthly data, ALWAYS include pos_data.
For questions about "how much" was sold over a time period, ALWAYS include pos_data.
FOR ANY queries about competitors, competing businesses, market comparison, or market share, ALWAYS include competitor_data.
For queries about policies, procedures, guidelines, or any document-related content, ALWAYS include document_data.

${documentContext ? `\n${documentContext}\n` : ""}
${competitorContext}

Response must be valid JSON in this format:
{
  "requiredContextTypes": ["context_type1", "context_type2"],
  "needsDocumentSearch": true/false
}

For needsDocumentSearch, set to true ONLY if the query appears to be asking about information that would be found in documents.

Only include context types that are directly relevant to answering the query.`,
              },
              {
                role: "user",
                content: message,
              },
            ],
            temperature: 0.3,
            response_format: { type: "json_object" },
            user: this.currentUserId ? String(this.currentUserId) : undefined,
          });

          const response = JSON.parse(
            completion.choices[0]?.message?.content || "{}"
          );

          return {
            intent: "agent_mentioned",
            confidence: 1.0,
            entities: {
              agentType: mentionedAgent,
              requiredContextTypes: response.requiredContextTypes || [],
              needsDocumentSearch: response.needsDocumentSearch || false,
            },
          };
        } catch (error) {
          logger.error({
            message: "Error classifying context for mentioned agent",
            error,
          });

          // Fallback if context classification fails
          return {
            intent: "agent_mentioned",
            confidence: 1.0,
            entities: {
              agentType: mentionedAgent,
              requiredContextTypes: [],
              needsDocumentSearch: false,
            },
          };
        }
      }

      // Check if it's a product recommendation query first
      // This is a special case handled separately
      if (this.isProductRecommendationQuery(message)) {
        logger.info({
          message: "Product recommendation query detected",
          user_message: message,
        });
        return {
          intent: "product_recommendation",
          confidence: 0.9,
          entities: {
            agentType: "SMOKEY",
            requiredContextTypes: ["product_data"],
            needsDocumentSearch: false,
          },
        };
      }

      // Use LLM for general intent classification
      const completion = await this.openai.chat.completions.create({
        model: "gpt-4.1-mini", // Using a smaller, faster model for classification
        messages: [
          {
            role: "system",
            content: `You are an intent classifier for a cannabis dispensary assistant.
Your job is to determine the user's intent and identify which data contexts are required to answer this query accurately.

Intent types:
- general_inquiry: General questions about cannabis, dispensary, products, etc.
- product_recommendation: Asking for product recommendations based on preferences, effects, or medical conditions
- sales_analysis: Questions about sales performance, revenue, trends
- marketing_campaign: Questions about marketing campaigns, promotional strategies
- financial_analysis: Questions about financial performance, profit margins, costs
- customer_relations: Questions about customer satisfaction, loyalty, feedback
- compliance_security: Questions about regulations, security, compliance
- document_inquiry: Questions about policies, procedures, guidelines
- image_generation: Requests to create images or visual content
- market_analysis: Questions about market trends, competitors, benchmarking

Data context types:
- product_data: Information about cannabis products, strains, inventory, pricing, THC/CBD content
- pos_data: Point of sale data, sales transactions, revenue, trends, bestsellers
- review_data: Customer reviews, ratings, feedback
- competitor_data: Information about competing dispensaries, their products, pricing
- retailer_data: Information about store locations, hours, details
- customer_data: Customer profiles, purchase history, preferences
- document_data: Information from uploaded documents, policies, and guidelines

${documentContext ? `\n${documentContext}\n` : ""}
${competitorContext}

IMPORTANT: For queries about bestsellers, popular products, top sellers, or most sold items, 
ALWAYS include both product_data AND pos_data in the context types.

IMPORTANT: For ANY query about competitors, market comparison, or benchmark,
ALWAYS include competitor_data in the context types.

IMPORTANT: For ANY query about internal policies, procedures, guidelines, or documents,
ALWAYS include document_data in the context types.

Response must be valid JSON in the following format:
{
  "intent": "intent_name",
  "confidence": 0.0-1.0,
  "entities": {
    "agentType": "AGENT_TYPE",
    "requiredContextTypes": ["context_type1", "context_type2"],
    "needsDocumentSearch": true/false
  }
}

For agentType, use:
- POPS: For sales_analysis
- SMOKEY: For product_recommendation
- CRAIG: For marketing_campaign and image_generation
- MONEY MIKE: For financial_analysis
- MRS. PARKER: For customer_relations 
- DEEBO: For compliance_security and document_inquiry
- EZAL: For market_analysis (ALWAYS use EZAL for any competitor-related or market comparison queries)
- null: For general_inquiry

For needsDocumentSearch, set to true ONLY if the query appears to be asking about information that would be found in documents.

Only include context types that are directly relevant to answering the query.`,
          },
          {
            role: "user",
            content: message,
          },
        ],
        temperature: 0.3,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const response = JSON.parse(
        completion.choices[0]?.message?.content || "{}"
      );

      // Ensure entities is present in the response
      if (!response.entities) {
        response.entities = {};
      }

      // Ensure requiredContextTypes is present in the response
      if (!response.entities.requiredContextTypes) {
        response.entities.requiredContextTypes = [];
      }

      // Set default value for needsDocumentSearch if not provided
      if (response.entities.needsDocumentSearch === undefined) {
        response.entities.needsDocumentSearch = false;
      }

      // Add special best-seller handling directly in this method
      // This ensures we get both product and sales data for questions about popular products
      if (
        response.entities.requiredContextTypes.includes("product_data") &&
        /popular|best([ -]?)sell|top|most sold/i.test(message) &&
        !response.entities.requiredContextTypes.includes("pos_data")
      ) {
        response.entities.requiredContextTypes.push("pos_data");
        logger.info({
          message: "Adding POS data context for bestseller/popularity query",
          types: response.entities.requiredContextTypes,
          query: message,
        });
      }

      // Add special competitor handling for queries that mention competitor names
      // This ensures we get competitor data even if the query doesn't explicitly mention "competitors"
      if (
        competitorNames.length > 0 &&
        !response.entities.requiredContextTypes.includes("competitor_data")
      ) {
        // Check if any competitor names are mentioned in the query
        const normalizedMessage = message.toLowerCase();
        const mentionsCompetitor = competitorNames.some((name) =>
          normalizedMessage.includes(name.toLowerCase())
        );

        if (mentionsCompetitor) {
          response.entities.requiredContextTypes.push("competitor_data");
          // Also set the agent type to EZAL for competitor-related queries
          if (response.entities.agentType !== "EZAL") {
            response.entities.agentType = "EZAL";
          }
          logger.info({
            message:
              "Adding competitor data context for competitor name mention",
            types: response.entities.requiredContextTypes,
            query: message,
          });
        }
      }

      // Add document_data if document_inquiry is detected
      if (
        response.intent === "document_inquiry" &&
        !response.entities.requiredContextTypes.includes("document_data")
      ) {
        response.entities.requiredContextTypes.push("document_data");
        response.entities.needsDocumentSearch = true;
        logger.info({
          message: "Adding document_data context for document inquiry",
          types: response.entities.requiredContextTypes,
          query: message,
        });
      }

      logger.info({
        message: "LLM intent and context classification",
        intent: response.intent,
        confidence: response.confidence,
        required_contexts: response.entities.requiredContextTypes,
        needs_document_search: response.entities.needsDocumentSearch,
        user_message: message,
      });

      return response;
    } catch (error) {
      logger.error({
        message: "Intent analysis failed",
        error,
      });

      // Return a default response with medium confidence
      return {
        intent: "general_inquiry",
        confidence: 0.5,
        entities: {
          agentType: undefined,
          requiredContextTypes: [],
          needsDocumentSearch: false,
        },
      };
    }
  }

  /**
   * Validates a response to ensure it meets quality and compliance standards
   */
  async validateResponse(response: string): Promise<boolean> {
    try {
      // Check for empty or extremely short responses
      if (!response || response.trim().length < 5) {
        logger.warn("Validation failed: Response too short or empty");
        return false;
      }

      // Check for inappropriate content using basic keyword filtering
      const inappropriateKeywords = [
        "illegal",
        "controlled substance",
        "trafficking",
        "underage",
        "tax evasion",
        "illicit",
        "prohibited",
      ];

      const containsInappropriate = inappropriateKeywords.some((keyword) =>
        response.toLowerCase().includes(keyword.toLowerCase())
      );

      if (containsInappropriate) {
        logger.warn(
          "Validation failed: Response contains potentially inappropriate content"
        );
        // In a real implementation, you might want to log the specific trigger keyword
        return false;
      }

      // Check compliance with cannabis marketing guidelines
      const complianceViolations = this.checkComplianceViolations(response);
      if (complianceViolations.length > 0) {
        logger.warn(
          `Validation failed: Compliance violations detected: ${complianceViolations.join(
            ", "
          )}`
        );
        return false;
      }

      // Ensure response format is valid by checking for common issues
      if (response.length > 10000) {
        logger.warn("Validation failed: Response too long");
        return false;
      }

      // Check for incomplete sentences at the end
      const endsWithSentence = /[.!?]\s*$/.test(response);
      if (!endsWithSentence) {
        logger.warn(
          "Validation failed: Response ends with incomplete sentence"
        );
        return false;
      }

      // All checks passed
      return true;
    } catch (error) {
      logger.error("Error validating response:", error);
      // Default to true in case of validation error to avoid blocking responses
      return true;
    }
  }

  /**
   * Checks for cannabis compliance violations in the response
   */
  private checkComplianceViolations(response: string): string[] {
    const violations: string[] = [];
    const lowerCaseResponse = response.toLowerCase();

    // Check for health claims
    const healthClaimPatterns = [
      /cure\s+(for|of)/i,
      /treat\s+(cancer|anxiety|depression|insomnia)/i,
      /medical\s+benefit/i,
      /therapeutic\s+effect/i,
      /health\s+benefit/i,
    ];

    // Check for marketing to minors
    const minorMarketingPatterns = [
      /kids/i,
      /children/i,
      /underage/i,
      /teen/i,
      /youth/i,
    ];

    // Check violations
    for (const pattern of healthClaimPatterns) {
      if (pattern.test(lowerCaseResponse)) {
        violations.push("health claim");
        break;
      }
    }

    for (const pattern of minorMarketingPatterns) {
      if (pattern.test(lowerCaseResponse)) {
        violations.push("marketing to minors");
        break;
      }
    }

    return violations;
  }

  /**
   * Gets all available agents for a location based on data availability
   */
  public async getAvailableAgents(locationId: number): Promise<Agent[]> {
    try {
      // Get availability results from the AgentAvailabilityService
      const availabilityResults =
        await AgentAvailabilityService.getAvailableAgents(locationId);

      // Convert availability results to Agent objects
      const agents: Agent[] = [];

      for (const result of availabilityResults) {
        // Only include fully available agents by default
        if (result.isAvailable) {
          const agentConfig = this.config.agents[result.agentId];
          if (!agentConfig) continue;

          agents.push({
            id: result.agentId,
            name: agentConfig.name,
            role: agentConfig.role,
            description: agentConfig.description,
            icon: agentConfig.icon || "🤖", // Provide default icon if undefined
            capabilities: agentConfig.capabilities || [],
            disabled: false,
            metadata: {
              availabilityPercentage: result.availabilityPercentage,
              dataStatus: result.dataStatus,
            },
          });
        }
      }

      // If no agents are fully available, include partially available agents
      if (agents.length === 0) {
        const partialAgents = availabilityResults.filter(
          (r) => r.partiallyAvailable && r.availabilityPercentage >= 50
        );

        for (const result of partialAgents) {
          const agentConfig = this.config.agents[result.agentId];
          if (!agentConfig) continue;

          agents.push({
            id: result.agentId,
            name: agentConfig.name,
            role: agentConfig.role,
            description: agentConfig.description,
            icon: agentConfig.icon || "🤖", // Provide default icon if undefined
            capabilities: agentConfig.capabilities || [],
            disabled: false,
            metadata: {
              availabilityPercentage: result.availabilityPercentage,
              dataStatus: result.dataStatus,
              missingRequirements: result.missingRequirements,
              partiallyAvailable: true,
            },
          });
        }
      }

      // If still no agents, return the default agent
      if (agents.length === 0) {
        agents.push(this.getDefaultAgent());
      }

      return agents;
    } catch (error) {
      logger.error("Error getting available agents:", error);
      return [this.getDefaultAgent()];
    }
  }

  /**
   * Returns a default agent when no specific agent is available
   */
  public getDefaultAgent(): Agent {
    return {
      id: "1",
      name: "Default Agent",
      role: "assistant",
      description: "Default chat assistant",
      icon: "🤖",
      capabilities: ["chat"],
      disabled: false,
      metadata: {},
    };
  }

  /**
   * Agent Selection Methods
   */
  async selectAgent(
    intent: IntentAnalysis,
    chatId: number,
    availableAgents: Agent[]
  ): Promise<Agent> {
    try {
      if (!availableAgents.length) {
        return this.getDefaultAgent();
      }

      // Check for explicit mentions first - highest priority
      if (
        intent.intent === "directed_message" &&
        intent.entities.mentionedAgent
      ) {
        const mentionedAgentName = intent.entities.mentionedAgent.trim();

        // Try to find by exact name match first (case insensitive)
        let matchingAgent = availableAgents.find(
          (agent) =>
            agent.name.toLowerCase() === mentionedAgentName.toLowerCase()
        );

        // If no exact match, try partial name match or check against role
        if (!matchingAgent) {
          matchingAgent = availableAgents.find(
            (agent) =>
              agent.name
                .toLowerCase()
                .includes(mentionedAgentName.toLowerCase()) ||
              agent.role
                .toLowerCase()
                .includes(mentionedAgentName.toLowerCase())
          );
        }

        if (matchingAgent) {
          logger.info({
            message: "Found agent matching user mention",
            agent_name: matchingAgent.name,
            mentioned_name: mentionedAgentName,
          });
          return matchingAgent;
        }

        // Log if mentioned agent wasn't found
        logger.warn({
          message: "Mentioned agent not found, falling back to intent matching",
          mentioned_name: mentionedAgentName,
        });
      }

      // If intent has agentType specified (either from mention or intent analysis), try to match that next
      if (intent.entities && intent.entities.agentType) {
        const targetAgentType = intent.entities.agentType;

        // Look for agent matching the detected agentType
        const matchingAgent = availableAgents.find(
          (agent) =>
            agent.name.toUpperCase() === targetAgentType.toUpperCase() ||
            agent.role.toUpperCase().includes(targetAgentType.toUpperCase())
        );

        if (matchingAgent) {
          logger.info({
            message: "Found agent matching intent type",
            agent_name: matchingAgent.name,
            intent_type: targetAgentType,
          });
          return matchingAgent;
        }
      }

      // Match based on intent itself if no explicit match was found
      // Map intents to likely agent roles or capabilities
      const intentToCapabilityMap: Record<string, string[]> = {
        sales_analysis: [
          "sales data",
          "analytics",
          "business intelligence",
          "performance",
        ],
        product_recommendation: [
          "product",
          "recommendation",
          "budtender",
          "customer",
        ],
        marketing_campaign: ["marketing", "campaign", "promotion"],
        financial_analysis: ["financial", "finance", "budget", "cost"],
        customer_relations: ["customer", "loyalty", "vip", "relations"],
        compliance_security: ["compliance", "security", "regulation"],
      };

      // Get capabilities related to this intent
      const relevantCapabilities = intentToCapabilityMap[intent.intent] || [];

      // Score each agent based on specialization and previous success
      const scores = await Promise.all(
        availableAgents.map(async (agent) => {
          // Check agent.capabilities against relevantCapabilities
          const capabilityMatches = agent.capabilities.filter((capability) =>
            relevantCapabilities.some((term) =>
              capability.toLowerCase().includes(term.toLowerCase())
            )
          ).length;

          // Check agent.role against relevant terms
          const roleMatch = relevantCapabilities.some(
            (term) =>
              agent.role.toLowerCase().includes(term.toLowerCase()) ||
              agent.description.toLowerCase().includes(term.toLowerCase())
          )
            ? 1
            : 0;

          // Calculate specialization score based on matches
          const specializationScore = capabilityMatches * 0.5 + roleMatch * 0.5;

          // Score based on previous success
          const successScore = await this.scorePreviousSuccess(agent, chatId);

          return {
            agent,
            score: specializationScore * 0.7 + successScore * 0.3,
            reasons: [
              `Specialization score: ${specializationScore.toFixed(2)}`,
              `Previous success score: ${successScore.toFixed(2)}`,
              `Capability matches: ${capabilityMatches}`,
              `Role match: ${roleMatch ? "Yes" : "No"}`,
            ],
          };
        })
      );

      // Log scores for debugging
      logger.debug({
        message: "Agent selection scores",
        intent: intent.intent,
        scores: scores.map((s) => ({
          agent_name: s.agent.name,
          score: s.score,
          reasons: s.reasons,
        })),
      });

      // Select the agent with the highest score
      const bestMatch = scores.reduce((best, current) =>
        current.score > best.score ? current : best
      );

      return bestMatch.agent;
    } catch (error) {
      logger.error("Error selecting agent:", error);
      return this.getDefaultAgent();
    }
  }

  private scoreSpecialization(agent: Agent, intent: IntentAnalysis): number {
    // Extract keywords from agent description and role
    const agentKeywords = [
      ...agent.description.toLowerCase().split(/\W+/),
      ...agent.role.toLowerCase().split(/\W+/),
    ];

    // Extract keywords from intent and entities
    const intentKeywords = [
      ...intent.intent.toLowerCase().split(/\W+/),
      ...Object.values(intent.entities).map((v) => v.toLowerCase()),
    ];

    // Count matching keywords
    const matches = intentKeywords.filter((keyword) =>
      agentKeywords.includes(keyword)
    );

    return matches.length / Math.max(intentKeywords.length, 1);
  }

  private async scorePreviousSuccess(
    agent: Agent,
    chatId: number
  ): Promise<number> {
    try {
      // This would need to be implemented with access to database
      // For now, returning a neutral score
      return 0.5;
    } catch (error) {
      logger.error("Error scoring previous success:", error);
      return 0.5; // Neutral score on error
    }
  }

  /**
   * Generates a title for a chat based on its message content
   */
  public async generateTitle(messages: Message[]): Promise<string> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content:
              "Generate a concise, descriptive title for this conversation that captures its main topic or purpose.",
          } as const,
          {
            role: "user",
            content: messages
              .slice(0, 5)
              .map((m) => m.content)
              .join("\n"),
          } as const,
        ],
        temperature: 0.3,
        max_tokens: 50,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      return completion.choices[0]?.message?.content || "Chat Conversation";
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";

      if (error?.status === 429) {
        errorType = "quota_exceeded";
      } else if (error?.status === 401) {
        errorType = "authentication_failed";
      } else if (error?.status === 403) {
        errorType = "forbidden";
      } else if (error?.status >= 500) {
        errorType = "server_error";
      } else if (error?.code === "insufficient_quota") {
        errorType = "quota_exceeded";
      } else if (error?.message?.includes("quota")) {
        errorType = "quota_exceeded";
      }

      logger.error({
        message: "Error generating chat title",
        errorType,
        error,
        errorMessage: error instanceof Error ? error.message : String(error),
        status: error?.status,
        code: error?.code,
        type: error?.type,
      });

      // For quota errors, log a specific warning
      if (errorType === "quota_exceeded") {
        logger.warn({
          message:
            "OpenAI quota exceeded for title generation - using fallback title",
          chatTitleFallback: true,
        });
      }

      return "Chat Conversation";
    }
  }

  /**
   * Generates a summary of the chat conversation
   */
  async generateChatSummary(messages: Message[]): Promise<string> {
    try {
      if (!messages || messages.length === 0) {
        return "No messages to summarize";
      }

      const formattedMessages = messages.map((msg) => ({
        role: msg.role === "user" ? ("user" as const) : ("assistant" as const),
        content: msg.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system" as const,
            content:
              "You are an assistant tasked with summarizing conversations. Create a concise summary that captures the main points, questions, and resolutions.",
          },
          ...formattedMessages,
        ],
        temperature: 0.3,
        max_tokens: 500,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      return (
        response.choices[0].message.content || "Failed to generate summary"
      );
    } catch (error) {
      logger.error("Error generating chat summary:", error);
      return "Failed to generate summary due to an error";
    }
  }

  /**
   * Extracts key topics from a chat conversation
   */
  async extractTopics(messages: Message[]): Promise<string[]> {
    try {
      if (!messages || messages.length === 0) {
        return [];
      }

      const formattedMessages = messages.map((msg) => ({
        role: msg.role === "user" ? ("user" as const) : ("assistant" as const),
        content: msg.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system" as const,
            content:
              "Identify the main topics discussed in this conversation. Return a JSON array of topic strings. Focus on product categories, business metrics, marketing strategies, or customer segments mentioned.",
          },
          ...formattedMessages,
        ],
        temperature: 0.2,
        max_tokens: 300,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      try {
        const content = response.choices[0].message.content || "{}";
        const parsed = JSON.parse(content);
        return Array.isArray(parsed.topics) ? parsed.topics : [];
      } catch (parseError) {
        logger.error("Error parsing topics JSON:", parseError);
        return [];
      }
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";

      if (
        error?.status === 429 ||
        error?.code === "insufficient_quota" ||
        error?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        logger.warn({
          message:
            "OpenAI quota exceeded for topic extraction - returning empty topics",
          topicExtractionFallback: true,
        });
      } else {
        logger.error({
          message: "Error extracting topics",
          errorType,
          error,
          errorMessage: error instanceof Error ? error.message : String(error),
          status: error?.status,
          code: error?.code,
        });
      }

      return [];
    }
  }

  /**
   * Extracts business metrics from chat conversations
   */
  async extractChatMetrics(messages: Message[]): Promise<{
    clv: number | null;
    roi: number | null;
    retention: number | null;
  }> {
    try {
      if (!messages || messages.length === 0) {
        return { clv: null, roi: null, retention: null };
      }

      const formattedMessages = messages.map((msg) => ({
        role: msg.role === "user" ? ("user" as const) : ("assistant" as const),
        content: msg.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system" as const,
            content:
              "Analyze this conversation for business metrics. Extract or estimate the following if mentioned: customer lifetime value (CLV), campaign return on investment (ROI), and customer retention rate. Return as a JSON object with keys 'clv', 'roi', and 'retention', with values as numbers or null if not found.",
          },
          ...formattedMessages,
        ],
        temperature: 0.1,
        max_tokens: 200,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      try {
        const content = response.choices[0].message.content || "{}";
        const parsed = JSON.parse(content);
        return {
          clv: typeof parsed.clv === "number" ? parsed.clv : null,
          roi: typeof parsed.roi === "number" ? parsed.roi : null,
          retention:
            typeof parsed.retention === "number" ? parsed.retention : null,
        };
      } catch (parseError) {
        logger.error("Error parsing metrics JSON:", parseError);
        return { clv: null, roi: null, retention: null };
      }
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";

      if (
        error?.status === 429 ||
        error?.code === "insufficient_quota" ||
        error?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        logger.warn({
          message:
            "OpenAI quota exceeded for chat metrics extraction - returning null metrics",
          metricsExtractionFallback: true,
        });
      } else {
        logger.error({
          message: "Error extracting chat metrics",
          errorType,
          error,
          errorMessage: error instanceof Error ? error.message : String(error),
          status: error?.status,
          code: error?.code,
        });
      }

      return { clv: null, roi: null, retention: null };
    }
  }

  /**
   * Analyzes a segment of chat messages to generate insights
   */
  async analyzeChatSegment(messages: Message[]): Promise<any[]> {
    try {
      if (!messages || messages.length === 0) {
        return [];
      }

      const formattedMessages = messages.map((msg) => ({
        role: msg.role === "user" ? ("user" as const) : ("assistant" as const),
        content: msg.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system" as const,
            content:
              "Analyze this conversation segment for business insights. Identify potential opportunities, customer pain points, and actionable recommendations. Return a JSON array of insights, where each insight has 'title', 'description', 'type' (opportunity, risk, feedback), and 'impact' (high, medium, low).",
          },
          ...formattedMessages,
        ],
        temperature: 0.4,
        max_tokens: 800,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      try {
        const content = response.choices[0].message.content || "{}";
        const parsed = JSON.parse(content);
        return Array.isArray(parsed.insights) ? parsed.insights : [];
      } catch (parseError) {
        logger.error("Error parsing insights JSON:", parseError);
        return [];
      }
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";

      if (
        error?.status === 429 ||
        error?.code === "insufficient_quota" ||
        error?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        logger.warn({
          message:
            "OpenAI quota exceeded for chat segment analysis - returning empty insights",
          chatAnalysisFallback: true,
        });
      } else {
        logger.error({
          message: "Error analyzing chat segment",
          errorType,
          error,
          errorMessage: error instanceof Error ? error.message : String(error),
          status: error?.status,
          code: error?.code,
        });
      }

      return [];
    }
  }

  /**
   * Get a list of competitor names for a specific location
   * This will be used to provide context in the system prompt
   */
  private async getCompetitorNamesForLocation(
    locationId: number
  ): Promise<string[]> {
    try {
      // Get competitors from the database
      const competitors = await this.db("location_competitors")
        .where({ location_id: locationId })
        .select("name")
        .orderBy("name", "asc");

      // Extract just the names
      return competitors.map((c) => c.name);
    } catch (error) {
      logger.error("Error getting competitor names:", error);
      return []; // Return empty array if there's an error
    }
  }

  /**
   * Helper method to get competitor names for a location and generate a system prompt with them included
   * This is useful for testing and debugging the competitor names feature
   */
  public async getSystemPromptWithCompetitors(
    locationId: number,
    agentId: string
  ): Promise<string> {
    try {
      const agentConfig = this.agentsConfig.agents[agentId];
      if (!agentConfig) {
        throw new Error(`Agent configuration not found for ID: ${agentId}`);
      }

      // Get the competitor names
      const competitorNames = await this.getCompetitorNamesForLocation(
        locationId
      );

      // Generate the system prompt
      const prompt = await this.buildToolSystemPrompt(
        agentConfig,
        true,
        undefined,
        locationId
      );

      return prompt;
    } catch (error) {
      logger.error("Error generating system prompt with competitors:", error);
      throw error;
    }
  }

  /**
   * Get a list of documents and their metadata for a location
   * This is used to pass to the intent analysis to determine if document search is needed
   */
  private async getLocationDocuments(
    locationId: number
  ): Promise<{ documents: any[] }> {
    try {
      const documents = await Document.query()
        .where({ location_id: locationId })
        .whereNull("deleted_at")
        .select("id", "name", "type", "created_at", "data")
        .orderBy("created_at", "desc");

      return { documents };
    } catch (error) {
      logger.error(
        `Error getting documents for location ${locationId}:`,
        error
      );
      return { documents: [] };
    }
  }
}
