#!/usr/bin/env node

/**
 * Test script for admin invitation email functionality
 * This script tests the AdminInvitationService to ensure emails are sent correctly
 */

// Load environment variables
require('dotenv').config();

const knex = require("knex");

// Database configuration (matching knexfile.ts and .env)
const dbConfig = {
  client: "mysql2",
  connection: {
    host: process.env.DB_HOST || "127.0.0.1",
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
  migrations: {
    directory: "./db/migrations",
    tableName: "migrations",
  },
};

// Initialize database connection
const db = knex(dbConfig);

async function testAdminInvitation() {
  try {
    console.log("🧪 Testing Admin Invitation Email Functionality...\n");

    // Get a sample organization and admin
    const [organizations] = await db.raw('SELECT * FROM organizations LIMIT 1');
    const [admins] = await db.raw('SELECT * FROM admins LIMIT 1');
    
    if (organizations.length === 0) {
      console.log("❌ No organizations found in database");
      return;
    }
    
    if (admins.length === 0) {
      console.log("❌ No admins found in database");
      return;
    }

    const organization = organizations[0];
    const invitingAdmin = admins[0];
    
    console.log(`📋 Test Data:`);
    console.log(`  Organization: ${organization.username} (ID: ${organization.id})`);
    console.log(`  Inviting Admin: ${invitingAdmin.email} (ID: ${invitingAdmin.id})`);
    console.log(`  Test Email: ${process.env.TEST_EMAIL || '<EMAIL>'}\n`);

    // Create a test admin object
    const testAdmin = {
      id: 999999, // Fake ID for testing
      email: process.env.TEST_EMAIL || '<EMAIL>',
      first_name: 'Test',
      last_name: 'Admin',
      role: 'admin',
      organization_id: organization.id,
    };

    // Import the AdminInvitationService
    const { AdminInvitationService } = require('../src/auth/AdminInvitationService');

    console.log("📧 Attempting to send invitation email...");
    
    const success = await AdminInvitationService.sendInvitationEmail({
      admin: testAdmin,
      organization: organization,
      invitedBy: invitingAdmin,
    });

    if (success) {
      console.log("✅ Invitation email sent successfully!");
      console.log(`📬 Check ${testAdmin.email} for the invitation email`);
    } else {
      console.log("❌ Failed to send invitation email");
      console.log("💡 This might be due to:");
      console.log("   - No email provider configured");
      console.log("   - Email provider not working");
      console.log("   - Network issues");
    }

  } catch (error) {
    console.error("❌ Error during test:", error.message);
    console.error("Stack trace:", error.stack);
  } finally {
    await db.destroy();
  }
}

async function testLocationAdminInvitation() {
  try {
    console.log("\n🧪 Testing Location Admin Invitation Email Functionality...\n");

    // Get sample data
    const [organizations] = await db.raw('SELECT * FROM organizations LIMIT 1');
    const [locations] = await db.raw('SELECT * FROM locations LIMIT 1');
    const [admins] = await db.raw('SELECT * FROM admins LIMIT 1');
    
    if (organizations.length === 0 || locations.length === 0 || admins.length === 0) {
      console.log("❌ Missing required test data (organizations, locations, or admins)");
      return;
    }

    const organization = organizations[0];
    const location = locations[0];
    const invitingAdmin = admins[0];
    
    console.log(`📋 Test Data:`);
    console.log(`  Organization: ${organization.username} (ID: ${organization.id})`);
    console.log(`  Location: ${location.name} (ID: ${location.id})`);
    console.log(`  Inviting Admin: ${invitingAdmin.email} (ID: ${invitingAdmin.id})`);
    console.log(`  Test Email: ${process.env.TEST_EMAIL || '<EMAIL>'}\n`);

    // Create a test admin object
    const testAdmin = {
      id: 999998, // Fake ID for testing
      email: process.env.TEST_EMAIL || '<EMAIL>',
      first_name: 'Test',
      last_name: 'LocationAdmin',
      role: 'member',
      organization_id: organization.id,
    };

    // Import the AdminInvitationService
    const { AdminInvitationService } = require('../src/auth/AdminInvitationService');

    console.log("📧 Attempting to send location invitation email...");
    
    const success = await AdminInvitationService.sendInvitationEmail({
      admin: testAdmin,
      organization: organization,
      invitedBy: invitingAdmin,
      location: location,
    });

    if (success) {
      console.log("✅ Location invitation email sent successfully!");
      console.log(`📬 Check ${testAdmin.email} for the location invitation email`);
    } else {
      console.log("❌ Failed to send location invitation email");
    }

  } catch (error) {
    console.error("❌ Error during location test:", error.message);
  }
}

async function main() {
  console.log("🚀 Starting Admin Invitation Email Tests...\n");
  
  await testAdminInvitation();
  await testLocationAdminInvitation();
  
  console.log("\n✨ Test completed!");
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { testAdminInvitation, testLocationAdminInvitation };
