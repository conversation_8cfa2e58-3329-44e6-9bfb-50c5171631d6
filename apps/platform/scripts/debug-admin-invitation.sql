-- Debug script for admin invitation email issue
-- Run this SQL to investigate why invitation emails are not being sent

-- 1. Check if the admin was created
SELECT 'STEP 1: Checking for admin <NAME_EMAIL>' as step;
SELECT id, email, first_name, last_name, role, organization_id, created_at
FROM admins 
WHERE email = '<EMAIL>' 
ORDER BY created_at DESC 
LIMIT 1;

-- 2. Check organization details for the admin
SELECT 'STEP 2: Checking organization details' as step;
SELECT o.id, o.username, o.domain, o.sender_email
FROM organizations o
JOIN admins a ON o.id = a.organization_id
WHERE a.email = '<EMAIL>'
LIMIT 1;

-- 3. Check locations in this organization
SELECT 'STEP 3: Checking locations in organization' as step;
SELECT l.id, l.name, l.organization_id
FROM locations l
JOIN admins a ON l.organization_id = a.organization_id
WHERE a.email = '<EMAIL>';

-- 4. Check email providers for these locations
SELECT 'STEP 4: Checking email providers' as step;
SELECT 
    p.id,
    p.name,
    p.type,
    p.group_name as provider_group,
    p.is_default,
    p.location_id,
    l.name as location_name,
    CASE 
        WHEN p.type = 'sendgrid' THEN 
            CASE 
                WHEN JSON_EXTRACT(p.data, '$.api_key') IS NOT NULL THEN 'Has API Key'
                ELSE 'Missing API Key'
            END
        ELSE 'N/A'
    END as api_key_status
FROM providers p
JOIN locations l ON p.location_id = l.id
JOIN admins a ON l.organization_id = a.organization_id
WHERE a.email = '<EMAIL>' 
AND p.group_name = 'email'
ORDER BY p.is_default DESC, p.created_at DESC;

-- 5. Check for default email providers specifically
SELECT 'STEP 5: Checking for default email providers' as step;
SELECT 
    p.id,
    p.name,
    p.type,
    p.location_id,
    l.name as location_name
FROM providers p
JOIN locations l ON p.location_id = l.id
JOIN admins a ON l.organization_id = a.organization_id
WHERE a.email = '<EMAIL>' 
AND p.group_name = 'email'
AND p.is_default = 1;

-- 6. Summary query
SELECT 'STEP 6: Summary' as step;
SELECT 
    COUNT(DISTINCT a.id) as admin_count,
    COUNT(DISTINCT l.id) as location_count,
    COUNT(DISTINCT p.id) as email_provider_count,
    COUNT(DISTINCT CASE WHEN p.is_default = 1 THEN p.id END) as default_provider_count
FROM admins a
LEFT JOIN locations l ON a.organization_id = l.organization_id
LEFT JOIN providers p ON l.id = p.location_id AND p.group_name = 'email'
WHERE a.email = '<EMAIL>';

-- 7. Check recent admins (in case email was different)
SELECT 'STEP 7: Recent admins created (last 24 hours)' as step;
SELECT id, email, first_name, last_name, role, organization_id, created_at
FROM admins 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY created_at DESC 
LIMIT 10;
