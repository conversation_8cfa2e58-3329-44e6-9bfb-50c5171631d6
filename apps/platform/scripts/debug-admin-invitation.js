#!/usr/bin/env node

/**
 * Debug script for admin invitation email issue
 * This script investigates why invitation emails are not being sent
 */

// Load environment variables
require('dotenv').config();

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || "127.0.0.1",
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
};

async function debugAdminInvitation() {
  let connection;
  
  try {
    console.log("🔍 Debugging Admin Invitation Email Issue...\n");

    // Connect to database
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ Connected to database\n");

    // 1. Check if the admin was created
    console.log("1️⃣ Checking for recently created admin with email '<EMAIL>'...");
    const [admins] = await connection.execute(`
      SELECT id, email, first_name, last_name, role, organization_id, created_at
      FROM admins 
      WHERE email = ? 
      ORDER BY created_at DESC 
      LIMIT 1
    `, ['<EMAIL>']);

    if (admins.length === 0) {
      console.log("❌ No admin found with email '<EMAIL>'");
      console.log("💡 This suggests the admin creation failed or used a different email");
      return;
    }

    const admin = admins[0];
    console.log("✅ Admin found:");
    console.log(`   ID: ${admin.id}`);
    console.log(`   Email: ${admin.email}`);
    console.log(`   Name: ${admin.first_name} ${admin.last_name}`);
    console.log(`   Role: ${admin.role}`);
    console.log(`   Organization ID: ${admin.organization_id}`);
    console.log(`   Created: ${admin.created_at}\n`);

    // 2. Check organization details
    console.log("2️⃣ Checking organization details...");
    const [organizations] = await connection.execute(`
      SELECT id, username, domain, sender_email
      FROM organizations 
      WHERE id = ?
    `, [admin.organization_id]);

    if (organizations.length === 0) {
      console.log("❌ Organization not found!");
      return;
    }

    const organization = organizations[0];
    console.log("✅ Organization found:");
    console.log(`   ID: ${organization.id}`);
    console.log(`   Username: ${organization.username}`);
    console.log(`   Domain: ${organization.domain}`);
    console.log(`   Sender Email: ${organization.sender_email}\n`);

    // 3. Check for locations in this organization
    console.log("3️⃣ Checking locations in this organization...");
    const [locations] = await connection.execute(`
      SELECT id, name, organization_id
      FROM locations 
      WHERE organization_id = ?
    `, [admin.organization_id]);

    console.log(`✅ Found ${locations.length} location(s):`);
    locations.forEach(loc => {
      console.log(`   Location ${loc.id}: ${loc.name}`);
    });
    console.log();

    // 4. Check for email providers
    console.log("4️⃣ Checking email providers for these locations...");
    let emailProviders = [];
    
    for (const location of locations) {
      const [providers] = await connection.execute(`
        SELECT id, name, type, group_name as 'group', is_default, data
        FROM providers 
        WHERE location_id = ? AND group_name = 'email'
        ORDER BY is_default DESC, created_at DESC
      `, [location.id]);

      if (providers.length > 0) {
        console.log(`✅ Location ${location.id} (${location.name}) has ${providers.length} email provider(s):`);
        providers.forEach(provider => {
          console.log(`   Provider ${provider.id}: ${provider.name} (${provider.type}) - Default: ${provider.is_default}`);
          
          // Check if it's SendGrid and show API key status
          if (provider.type === 'sendgrid') {
            try {
              const data = JSON.parse(provider.data);
              const hasApiKey = !!data.api_key;
              const apiKeyPreview = hasApiKey ? `${data.api_key.substring(0, 6)}...` : 'None';
              console.log(`     API Key: ${hasApiKey ? '✅' : '❌'} ${apiKeyPreview}`);
            } catch (e) {
              console.log(`     API Key: ❌ Invalid data format`);
            }
          }
        });
        emailProviders = emailProviders.concat(providers);
      } else {
        console.log(`❌ Location ${location.id} (${location.name}) has no email providers`);
      }
    }

    if (emailProviders.length === 0) {
      console.log("\n❌ NO EMAIL PROVIDERS FOUND!");
      console.log("💡 This is likely why no invitation email was sent.");
      console.log("🔧 To fix this:");
      console.log("   1. Go to Location Settings → Providers");
      console.log("   2. Add an email provider (SendGrid, Mailgun, SMTP, etc.)");
      console.log("   3. Configure it with valid credentials");
      console.log("   4. Set it as default");
      return;
    }

    console.log(`\n✅ Found ${emailProviders.length} total email provider(s)\n`);

    // 5. Test the AdminInvitationService logic
    console.log("5️⃣ Testing AdminInvitationService email provider detection...");
    
    // Simulate the service logic
    let foundProvider = null;
    for (const location of locations) {
      const [providers] = await connection.execute(`
        SELECT id, name, type, is_default
        FROM providers 
        WHERE location_id = ? AND group_name = 'email' AND is_default = 1
        ORDER BY created_at DESC
      `, [location.id]);

      if (providers.length > 0) {
        foundProvider = providers[0];
        console.log(`✅ Found default email provider: ${foundProvider.name} (ID: ${foundProvider.id})`);
        break;
      }
    }

    if (!foundProvider) {
      console.log("❌ No default email provider found!");
      console.log("💡 The AdminInvitationService requires a default email provider.");
      console.log("🔧 To fix this:");
      console.log("   1. Go to Location Settings → Providers");
      console.log("   2. Edit an existing email provider");
      console.log("   3. Check 'Set as default'");
      console.log("   4. Save the configuration");
      return;
    }

    // 6. Check application logs or console output
    console.log("\n6️⃣ Checking for potential issues...");
    
    // Check if the AdminController was properly updated
    console.log("✅ AdminController should be sending invitation emails");
    console.log("✅ AdminInvitationService should be available");
    
    console.log("\n🎯 SUMMARY:");
    console.log(`✅ Admin created: ${admin.email}`);
    console.log(`✅ Organization exists: ${organization.username}`);
    console.log(`✅ Locations found: ${locations.length}`);
    console.log(`${emailProviders.length > 0 ? '✅' : '❌'} Email providers: ${emailProviders.length}`);
    console.log(`${foundProvider ? '✅' : '❌'} Default provider: ${foundProvider ? foundProvider.name : 'None'}`);

    if (emailProviders.length > 0 && foundProvider) {
      console.log("\n💡 Email infrastructure looks good. Possible issues:");
      console.log("   1. Application server might not be running the updated code");
      console.log("   2. Email provider credentials might be invalid");
      console.log("   3. Email might be in spam folder");
      console.log("   4. Email provider might be rate limiting");
      console.log("   5. Check application logs for error messages");
    }

  } catch (error) {
    console.error("❌ Error during debug:", error.message);
    console.error("Stack trace:", error.stack);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
if (require.main === module) {
  debugAdminInvitation();
}

module.exports = { debugAdminInvitation };
